# Cloud Mono Repositories

Setup Cloud Deployment made easy with ArgoCD.

## Project Structure
- **argocd-apps**: Contains Application Set to deploy with ArgoCDs exists in `k8s` foler including:
  - `cloud-api`: API application was developed by Go
  - `cloud-job`: Cron job to fetch ECR token for fleet management to update private registry secret for edge devices.
  - `cloud-ui`: SPA application was developed by ReactJS
  - `fleet-management`: Control Plane of Edge Devices was developed by Go.
- **docker**: Contains Docker files use for `cloud-job`. On EKS, it's used with AMD arch. On Edge Devices, it's used with ARM arch.
- **gitlab**: Contains Gitlab CI/CD manifests for multiple repositories.
- **k8s**: Contains K8S manifests for Cloud Deployment with ArgoCDs.
- **scripts**: Contains script to use for ArgoCD in CI/CD pipeline, and other ultilies.

## GitOps with Gitlab CI & Kubernetes
### About GitOps?
![GitOps](images/2.png "GitOps Flow")
- The entire system (including infrastructure and application) will be described through declarations - declaratively.
- The actual state of the system will be represented through a git tree.
- Versioned and Immutable - Desired state is stored in a way that enforces immutability, versioning and retains a complete version history.
- Read more details here: https://opengitops.dev
### ArgoCD tool
![ArgoCD](images/3.png "ArgoCD Flow")
- You install Argo CD as a controller in the Kubernetes cluster. Usually you install Argo CD on the same cluster that it manages. It is also possible for Argo CD to manage external clusters.
- You store your manifests in Git. Argo CD is agnostic on the type of manifests you can use. It supports plain Kubernetes manifests, Helm charts, Kustomize definitions, and other templating mechanisms.
- You create an Argo CD application by defining which Git repository to monitor and to which cluster/namespace this application should be installed.
- From now on, Argo CD monitors the Git repository, and when there is a change, it automatically brings the cluster to the same state.
- Optionally Argo CD can deploy applications to other clusters (and not just the one on which it is installed).
### Implementation
![Implementation](images/flow.jpeg "How to implement?")

## Get Started

### Configure Gitlab CI/CD
- Visit to `gitlab/gitlab-ci.template.yml` and modify all base variables for your environment. For example:

    ```
    .gstaging:
    variables:
        AWS_ACCOUNT_ID: "" # AWS Account ID uses to pull and push image with ECR.
        KUSTOMIZE_ENV: staging #Environment folder in cloud-mono-repo.

    .staging:
    extends:
        - .gstaging
    tags:
        - mve2-staging-runner # Gitlab Runner to build AMD image (All cloud Docker images should build with this)

    .staging_orin:
    extends:
        - .gstaging
    tags:
        - mve2-staging-orin-runner # Gitlab Runner to build ARM image with Orin devices.
    ```

- Visit to each folder application to update `.gitlab-ci.yml` manifest will being used at its application repository. For example:
  - Visit to `cloud-api` and update for new environment:
  ```
    build_staging:
    extends:
        - .cloud_api
        - .build
        - .staging

    deploy_staging:
    extends:
        - .cloud_api
        - .deploy
        - .staging
    needs:
        - job: build_staging
        artifacts: true
  ```
- After completed above two steps, you can visit to the CI/CD settings at application repository, and configure Gitlab CI/CD manifest in General Pipelines section.

### Configure Cloud Deployment
- Visit to `k8s/application-folder`
- Create new environment folder in `envs`
- Update required information for your specific environment.
- For `fleet-management` with Baetyl, please carefully update `baetyl-init-deployment.yml` in `templates` folder including:
  - `ecr-helper-secret`: Update AWS credentials to be able pulling Docker image from AWS ECR. (You can found this one in `iam` modules of Terraform repository.)
  - `mve-worker-configmap`: Update Cloud API addresses
  - `mve-worker-secrets`: Update MQTT client certificates (You can found this one in `k8s` modules of Terraform repository).
  - `fluent-bit`: Update loki gateway password credentials (You can found this one in `k8s` modules of Terraform registry.) Default username is `loki`.
  - `Docker image`: Please update AWS_ACCOUNT_ID / ECR repository to be able pull edge device images from AWS ECR.

### Cloud Fleet Management Deplyoment DNS
- Please create new DNS record to AWS Load Balancer in K8S service for Cloud Fleet Management after deployed succesfully with ArgoCDs.
- For example: `sync.mve2-dev.knizsoft.com` to DNS address of Load Balancer in `baetyl-cloud` namespace.

### Endpoints
These are default endpoints of dev environment:
- Cloud UI: `mve2-dev.knizsoft.com`
- Cloud API: `api.mve2-dev.knizsoft.com`
- Fleet Management:
  - `init.mve2-dev.knizsoft.com`
  - `sync.mve2-dev.knizsoft.com`
- Grafana Dashboard: `grafana.mve2-dev.knizsoft.com`
- Loki Gateway: `loki.mve2-dev.knizsoft.com`
- MQTT: `mqtt.mve2-dev.knizsoft.com`
- S3 Asset Bucket: `mve2-dev-asset.knizsoft.com`