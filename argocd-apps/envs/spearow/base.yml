apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: mve2-deployment-application-set
spec:
  generators:
    - list:
        elements:
          - url: "https://kubernetes.default.svc"
            env: "spearow"
            name: "cloud-api"
            path: "k8s/cloud-api/envs/spearow"

          - url: "https://kubernetes.default.svc"
            env: "spearow"
            name: "cloud-ui"
            path: "k8s/cloud-ui/envs/spearow"

          - url: "https://kubernetes.default.svc"
            env: "spearow"
            name: "fleet-management"
            path: "k8s/fleet-management/envs/spearow"

          - url: "https://kubernetes.default.svc"
            env: "spearow"
            name: "cloud-job"
            path: "k8s/cloud-job/envs/spearow"

      selector:
        matchLabels:
          env: "spearow"

  template:
    metadata:
      name: "{{name}}"
    spec:
      project: mve2-spearow-cloud
      source:
        repoURL: https://gitlab.knizsoft.com/delivery/longclawx01/mercedes/cloud-mono-repo.git
        targetRevision: main
        path: "{{path}}"
      destination:
        server: "{{url}}"
      syncPolicy:
        automated:
          prune: true # Specifies if resources should be pruned during auto-syncing ( false by default ).
          selfHeal: true # Specifies if partial app sync should be executed when resources are changed only in target Kubernetes cluster and no git change detected ( false by default ).
