apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: mve2-deployment-application-set
spec:
  generators:
    - list:
        elements:
          - url: "https://kubernetes.default.svc"
            env: "staging" #CHANGE_FOR_DIFF_ENV
            name: "cloud-api"
            path: "k8s/cloud-api/envs/staging" #CHANGE_FOR_DIFF_ENV

          - url: "https://kubernetes.default.svc"
            env: "staging" #CHANGE_FOR_DIFF_ENV
            name: "cloud-ui"
            path: "k8s/cloud-ui/envs/staging" #CHANGE_FOR_DIFF_ENV

          - url: "https://kubernetes.default.svc"
            env: "staging" #CHANGE_FOR_DIFF_ENV
            name: "fleet-management"
            path: "k8s/fleet-management/envs/staging" #CHANGE_FOR_DIFF_ENV

          - url: "https://kubernetes.default.svc"
            env: "staging" #CHANGE_FOR_DIFF_ENV
            name: "cloud-job"
            path: "k8s/cloud-job/envs/staging" #CHANGE_FOR_DIFF_ENV

      selector:
        matchLabels:
          env: "staging" #CHANGE_FOR_DIFF_ENV

  template:
    metadata:
      name: "{{name}}"
    spec:
      project: mve2-staging-cloud #CHANGE_FOR_DIFF_ENV
      source:
        repoURL: https://gitlab.knizsoft.com/delivery/longclawx01/mercedes/cloud-mono-repo.git #CHANGE_IF_NOT_USING_KNOVEL
        targetRevision: main
        path: "{{path}}"
      destination:
        server: "{{url}}"
      syncPolicy:
        automated:
          prune: true # Specifies if resources should be pruned during auto-syncing ( false by default ).
          selfHeal: true # Specifies if partial app sync should be executed when resources are changed only in target Kubernetes cluster and no git change detected ( false by default ).
