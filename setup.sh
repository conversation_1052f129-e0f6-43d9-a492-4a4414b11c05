#!/bin/sh

ADDR="https://init.mve2-staging.knizsoft.com"
DEPLOYYML="baetyl-init-deployment.yml"
DB_PATH='/var/lib/baetyl'
TOKEN="217a59e0cf7b2265223a313734333736313333312c226e223a2265313132333539372d633032302d346461642d626665342d376263663464313531326162222c226e73223a2262616574796c2d636c6f7564227d"
MODE='kube'
SUDO='sudo'
KUBECONFIG="/home/<USER>/.kube/kubeconfig" # This is default kubeconfig to be used by kubectl/k9s

AP_PASSWORD="mve2@321"
WIFI_IF="muap0"
MOBILE_IF=wwan0
ETHERNET_IF=eth0

DOCKERDAEMON='{ \
    \"runtimes\": {\
        \"nvidia\": {\
            \"path\": \"nvidia-container-runtime\",\
            \"runtimeArgs\": []\
        }\
    },\
    \"default-runtime\": \"nvidia\",\
    \"data-root\": \"/data/docker\"\
}'

# if /dev/sda exists, use it, otherwise use /dev/nvme0n1
if [ -b "/dev/sda" ]; then
    DISK="/dev/sda"
    PARTITION="${DISK}1"
    MOUNT_POINT="/data"
else
    DISK="/dev/nvme0n1"
    PARTITION="${DISK}p1"
    MOUNT_POINT="/data"
fi

# Exec cmd and not exit if error
exec_cmd_nobail() {	
  echo "+ $2 bash -c \"$1\""	
  $2 bash -c "$1"	
}

# Exec cmd and exit if error
exec_cmd() {
  local status=1
  while [ $status -ne 0 ]; do
    echo "+ $2 bash -c \"$1\""	
    $2 bash -c "$1"
    status=$?
    if [ $status -ne 0 ]; then
      echo "Error with $2 bash -c \"$1\", retrying..." >&2
      sleep 2
    fi
  done
  return $status
}

print_status() {	
  echo "## $1"	
}

set_time_zone() {
  exec_cmd_nobail "timedatectl set-timezone "Asia/Singapore"" $SUDO
}


##################################
########## KUBE Setting ##########
##################################
install_kubeconfig() {
  exec_cmd "chown -R $USER:$USER /etc/rancher/k3s/k3s.yaml" $SUDO
  exec_cmd "mkdir -p /home/<USER>/.kube"
  exec_cmd "cp /etc/rancher/k3s/k3s.yaml /data/cluster_data/data/kubeconfig"
  exec_cmd "rm -rf $KUBECONFIG" $SUDO
  exec_cmd "cp /etc/rancher/k3s/k3s.yaml $KUBECONFIG"
}

install_k3s() {
  exec_cmd 'curl -sfL https://get.k3s.io/ | INSTALL_K3S_VERSION="v1.27.16+k3s1" INSTALL_K3S_EXEC="--docker  --disable local-storage --disable traefik --write-kubeconfig-mode 0644" sh -s -'
  install_kubeconfig
}

install_k9s() {
  exec_cmd 'curl -LO https://github.com/derailed/k9s/releases/download/v0.40.4/k9s_Linux_arm64.tar.gz && tar -vzxf k9s_Linux_arm64.tar.gz' $SUDO
  exec_cmd 'mv k9s /usr/local/bin' $SUDO
  exec_cmd 'echo "export KUBECONFIG=/home/<USER>/.kube/kubeconfig" >> ~/.bashrc'
}

dbfile_clean() {
  INIT_DBFILE=$DB_PATH/store/core.db
  CORE_DBFILE=$DB_PATH/init/store/core.db
  if [ -f $INIT_DBFILE ]; then
    exec_cmd_nobail "rm -rf $INIT_DBFILE" $SUDO
    print_status "init db file deleted"
  fi
  if [ -f $CORE_DBFILE ]; then
    exec_cmd_nobail "rm -rf $CORE_DBFILE" $SUDO
    print_status "core db file deleted"
  fi
}

kube_clean() {
  exec_cmd "/usr/local/bin/kubectl --kubeconfig $KUBECONFIG delete clusterrolebinding baetyl-edge-system-rbac --ignore-not-found=true" 
  exec_cmd "/usr/local/bin/kubectl --kubeconfig $KUBECONFIG delete ns baetyl-edge-system --ignore-not-found=true" 
}

download_tool=curl
check_download_tool() {
	if [ -x "$(command -v wget)" ]; then
		download_tool=wget
	fi
}

kube_apply() {	
  TempFile=$(mktemp temp.XXXXXX)
  if [ "$download_tool" = "wget" ]; then
    exec_cmd "wget --no-check-certificate -O $TempFile \"$1\"" 
  else
    exec_cmd "curl -skfL \"$1\" >$TempFile"
  fi
  if ! exec_cmd "/usr/local/bin/kubectl --kubeconfig $KUBECONFIG apply -f $TempFile"; then
    exec_cmd "rm -f $KUBECONFIG" $SUDO
  fi
  exec_cmd "rm -f $TempFile 2>/dev/null" $SUDO
}

verify_k8s_api() {
  print_status "Verifying Kubernetes API server accessibility..."
  local max_attempts=30
  local attempt=1
  
  while [ $attempt -le $max_attempts ]; do
    if kubectl --kubeconfig $KUBECONFIG get nodes >/dev/null 2>&1; then
      print_status "Kubernetes API server is accessible"
      return 0
    fi
    
    print_status "Attempt $attempt/$max_attempts: Waiting for API server..."
    sleep 10
    attempt=$((attempt + 1))
  done
  
  print_status "ERROR: Kubernetes API server is not accessible after $max_attempts attempts"
  return 1
}

install_baetyl() {
  dbfile_clean
  if [ $MODE = "kube" ]; then
    print_status "baetyl install in k8s mode"
    kube_clean
    kube_apply "$ADDR/v1/init/$DEPLOYYML?token=$TOKEN"
  elif [ $MODE = "native" ]; then
    print_status "baetyl install in native mode"
    exec_cmd "baetyl delete" $SUDO
    exec_cmd "baetyl apply -f '$ADDR/v1/init/$DEPLOYYML?token=$TOKEN' --skip-verify=true" $SUDO
  else
    print_status "Not supported install mode $MODE"
    exit 0
  fi
}

prepare_hostpath() {	
  exec_cmd "mkdir -p /data/cluster_data" $SUDO
  exec_cmd "chown -R $USER:$USER /data/cluster_data " $SUDO
  exec_cmd "mkdir -p /data/cluster_data" 
  exec_cmd "mkdir -p /data/cluster_data/data/images/" 
  exec_cmd "mkdir -p /data/cluster_data/data/videos/" 
  exec_cmd "mkdir -p /data/cluster_data/database/" 
}

save_device_id() {
  exec_cmd_nobail "rm -f /home/<USER>/.device_id" $SUDO
  exec_cmd_nobail "echo $DEVICE_ID > /home/<USER>/.device_id"
}

prepare_apt() {
  # sudo rm -rf /var/lib/apt/lists/*
  sudo apt update -y
  sudo apt install -y tar jq
  sudo apt install apt-utils -y
}


##################################
########## Disk Setting ##########
##################################
mount_partition() {
  # Check if $PARTITION exists
  if [ ! -b "$PARTITION" ]; then
    echo "Error: $PARTITION does not exist."
    exit 0
  fi

  # Create folder /data if not exist
  if [ ! -d "/data" ]; then
    echo "Creating /data directory..."
    sudo mkdir -p /data
  fi

  echo "Updating /etc/fstab..."
  sudo sed -i '/\/data ext4 default/d' /etc/fstab

  if ! mount | grep -q "$PARTITION on /data"; then
    echo "Mounting $PARTITION to /data..."
    echo "$PARTITION /data ext4 rw 0 1" | sudo tee -a /etc/fstab >/dev/null
    sudo mount -o rw "$PARTITION" /data
  else
    echo "$PARTITION is already mounted on /data."
  fi

}



##################################
########## Docker Setting ##########
##################################
mount_docker(){
  exec_cmd_nobail "groupadd docker" $SUDO
  exec_cmd_nobail "usermod -aG docker $USER" $SUDO
  exec_cmd_nobail "mkdir -p /data/docker" $SUDO # create folder
  exec_cmd_nobail "echo $DOCKERDAEMON> /etc/docker/daemon.json" $SUDO # create daemon.json
  exec_cmd_nobail "systemctl restart docker" $SUDO # restart docker
}

clean_docker(){
  exec_cmd_nobail "bash /usr/local/bin/k3s-uninstall.sh"
  # Remove all docker container
  docker stop $(docker ps -q)
  docker rm $(docker ps -a -q)
  docker rmi -f $(docker images -q)

  # Stop docker service
  exec_cmd_nobail "systemctl stop docker" $SUDO
  exec_cmd_nobail "systemctl stop docker.socket" $SUDO
  
  # Remove package
  exec_cmd_nobail "apt-get purge docker.io docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin docker-ce-rootless-extras -y" $SUDO
  
  # Remove file and folder
  exec_cmd_nobail "rm -rf /var/lib/docker" $SUDO
  exec_cmd_nobail "rm -rf /var/lib/containerd" $SUDO
  exec_cmd_nobail "rm -rf /data/docker/" $SUDO

  # Remove unused package
  exec_cmd_nobail "apt-get autoremove -y" $SUDO
  exec_cmd_nobail "apt-get autoclean" $SUDO

  exec_cmd_nobail "rm -rf /data/docker/" $SUDO
}

install_docker(){
  echo "Cleaning Docker..."
  clean_docker

  echo "Installing Docker..."
  # Url download bash script
  DESIRED_VERSION="26.1.3"
  DOCKER_INSTALL_URL="https://releases.rancher.com/install-docker/$DESIRED_VERSION.sh"
  DOCKER_SCRIPT="install-docker-$DESIRED_VERSION.sh"

  # Download setting script
  exec_cmd_nobail "rm -rf $DOCKER_SCRIPT" $SUDO
  echo "Downloading $DOCKER_INSTALL_URL ..."
  exec_cmd "curl -fsSL $DOCKER_INSTALL_URL -o $DOCKER_SCRIPT"

  if [ ! -f "$DOCKER_SCRIPT" ]; then
      echo "Error: Can not download file $DOCKER_SCRIPT."
      exit 1
  fi

  chmod +x "$DOCKER_SCRIPT"

  # Install Docker
  echo "Installing Docker $DESIRED_VERSION ..."
  sudo ./"$DOCKER_SCRIPT"

  mount_docker

  sleep 5

  # check /usr/bin/docker is exist
  if [ ! -f "/usr/bin/docker" ]; then
    echo "Error: Docker not found."
    exit 1
  fi

  # Remove script
  echo "Removing script ..."
  rm -f "$DOCKER_SCRIPT"

  echo "Install docker completed."
}


##################################
########## GSM Setting ##########
##################################
setup_gsm() {
  CONN_NAME=eight-mobile
  APN_NAME=shwap
  IF_NAME=cdc-wdm0

  # check if the connection already exists
  if nmcli -t -f NAME con show | grep -q "^$CONN_NAME$"; then
    echo "Connection $CONN_NAME exists."
    return
  fi

  # Setting up 8eight sim card
  exec_cmd_nobail "nmcli c add type gsm con-name $CONN_NAME ifname $IF_NAME apn $APN_NAME" $SUDO

  # Setting up the properties 
  exec_cmd_nobail "nmcli con modify $CONN_NAME connection.autoconnect yes" $SUDO
  exec_cmd_nobail "nmcli con modify $CONN_NAME connection.autoconnect-priority 10" $SUDO
  exec_cmd_nobail "nmcli con modify $CONN_NAME connection.autoconnect-retries 3" $SUDO
  exec_cmd_nobail "nmcli con modify $CONN_NAME ipv4.route-metric 400" $SUDO

  # Up the connection
  exec_cmd_nobail "nmcli con up $CONN_NAME" $SUDO

  # Ensure that the radio interface is on
  exec_cmd_nobail "nmcli r wwan on" $SUDO
}


##################################
########## AP Mode Setting ##########
##################################
ip_forwarding(){
    # Enable IP Forwarding
    exec_cmd_nobail "sysctl -w net.ipv4.ip_forward=1" $SUDO

    # Set Up NAT (Masquerading)
    exec_cmd_nobail "iptables -t nat -A POSTROUTING -o $MOBILE_IF -j MASQUERADE"  $SUDO
    exec_cmd_nobail "iptables -t nat -A POSTROUTING -o $ETHERNET_IF -j MASQUERADE" $SUDO
    # Allow Forwarding from Wi-Fi to Internet
    exec_cmd_nobail "iptables -A FORWARD -i $WIFI_IF -o $MOBILE_IF -j ACCEPT" $SUDO
    exec_cmd_nobail "iptables -A FORWARD -i $WIFI_IF -o $ETHERNET_IF -j ACCEPT" $SUDO
    # Allow Established Connections Back
    exec_cmd_nobail "iptables -A FORWARD -i $MOBILE_IF -o $WIFI_IF -m state --state ESTABLISHED,RELATED -j ACCEPT" $SUDO
    exec_cmd_nobail "iptables -A FORWARD -i $ETHERNET_IF -o $WIFI_IF -m state --state ESTABLISHED,RELATED -j ACCEPT" $SUDO
}

ap_config(){
    ID=$1
    CONN_NAME=orin-ap-${ID}

    # Check if the connection for muap0 device is existed, it yes -> return 
    echo "Checking connections for device $WIFI_IF..."
    CONNECTIONS=$(nmcli -t -f NAME,DEVICE connection show | grep "$WIFI_IF" | cut -d':' -f1)
    # Return if have connection
    if [ -n "$CONNECTIONS" ]; then
        echo "Found the following connections for device $WIFI_IF. No setup required."
        return
    fi
    

    # Disable station mode
    if nmcli -t -f DEVICE,STATE device | grep -q '^mlan0:connected$'; then
        echo "mlan0 is active"
        exec_cmd_nobail "nmcli device disconnect mlan0" $SUDO
        CONN_NAME=$(nmcli -t -f NAME,DEVICE con show | grep 'mlan0' | cut -d: -f1)
        # Check if a connection was found
        if [ -n "$CONN_NAME" ]; then
            echo "Deleting connection: $CONN_NAME"
            exec_cmd_nobail "nmcli con delete $CONN_NAME" $SUDO
            echo "Connection deleted successfully."
        else
            echo "No connection found for mlan0."
        fi
    else
        echo "mlan0 is not active"
    fi

    if nmcli -t -f DEVICE,STATE device | grep -q '^mmlan0:connected$'; then
        echo "mmlan0 is active"
        sleep 1
        exec_cmd_nobail "nmcli device disconnect mmlan0" $SUDO
        CONN_NAME=$(nmcli -t -f NAME,DEVICE con show | grep 'mmlan0' | cut -d: -f1)
        sleep 1
        # Check if a connection was found
        if [ -n "$CONN_NAME" ]; then
            echo "Deleting connection: $CONN_NAME"
            exec_cmd_nobail "nmcli con delete $CONN_NAME" $SUDO
            echo "Connection deleted successfully."
        else
            echo "No connection found for mmlan0."
        fi
    else
        echo "mmlan0 is not active"
    fi

    # Create AP using muap0 interface
    exec_cmd_nobail "nmcli device wifi hotspot ifname $WIFI_IF con-name $CONN_NAME ssid $CONN_NAME" $SUDO

    # Set WiFi settings
    exec_cmd_nobail "nmcli con modify $CONN_NAME 802-11-wireless.ssid $CONN_NAME" $SUDO
    exec_cmd_nobail "nmcli con modify $CONN_NAME 802-11-wireless.band bg" $SUDO
    exec_cmd_nobail "nmcli con modify $CONN_NAME 802-11-wireless-security.key-mgmt wpa-psk" $SUDO
    exec_cmd_nobail "nmcli con modify $CONN_NAME 802-11-wireless-security.psk ${AP_PASSWORD}" $SUDO
    exec_cmd_nobail "nmcli con modify $CONN_NAME 802-11-wireless.powersave disable" $SUDO

    # Configure IP & Internet Sharing
    exec_cmd_nobail "nmcli con modify $CONN_NAME ipv4.method shared" $SUDO
    exec_cmd_nobail "nmcli con modify $CONN_NAME ipv4.addresses *************/24" $SUDO
    exec_cmd_nobail "nmcli con modify $CONN_NAME ipv4.gateway ***********" $SUDO
    exec_cmd_nobail "nmcli con modify $CONN_NAME ipv4.route-metric 700" $SUDO

    # Enable auto-connect
    exec_cmd_nobail "nmcli con modify $CONN_NAME connection.autoconnect yes" $SUDO
    exec_cmd_nobail "nmcli con modify $CONN_NAME connection.autoconnect-priority 100" $SUDO

    # Activate the AP
    exec_cmd_nobail "nmcli con up $CONN_NAME" $SUDO

    # Sleep to avoid busy on wifi driver
    sleep 30
}

setup_ap_mode (){
  # Check if DEVICE_ID is set
  if [ -z "$DEVICE_ID" ]; then
    echo "Error: DEVICE_ID is not set"
    exit 0
  fi

  # Get 7 last characters of DEVICE_ID
  ID=$(echo "$DEVICE_ID" | tail -c 8)  # 8 is the tail character
  echo "ID: $ID"

  # Check if connection to muap0 device is existed
  if nmcli -t -f DEVICE con show | grep -q "^muap0$"; then
      echo "Connection muap0 exists."
      # Check if the connection name is orin-ap-default then change to orin-ap-${ID}
      if nmcli -t -f NAME con show | grep -q "^orin-ap-default$"; then
        echo "Connection name is orin-ap-default, changing to orin-ap-${ID}"
        exec_cmd_nobail "nmcli con modify orin-ap-default 802-11-wireless.ssid orin-ap-${ID}" $SUDO
        exec_cmd_nobail "nmcli con modify orin-ap-default connection.id orin-ap-${ID}" $SUDO
      fi
  else
      # If not, create a new one
      echo "Connection muap0 does NOT exist. Create new connection name orin-ap-${ID}"
      ap_config $ID
      ip_forwarding
  fi
}


##################################
# Main
##################################

echo "Set Timezone..."
set_time_zone
echo "Mount Partition..."
mount_partition
echo "Prepare APT..."
prepare_apt
echo "Prepare Hostpath..."
prepare_hostpath
echo "Install Docker..."
install_docker
echo "Setup GSM..."
setup_gsm
echo "Setup AP Mode..."
setup_ap_mode
echo "Install K3S Cluster..."
install_k3s
echo "Waiting 30s for K8S cluster ready..."
sleep 30
echo "Verifying K8S API accessbility..."
verify_k8s_api      
if [ $? -ne 0 ]; then
  echo "Failed to verify K8S API accessibility. Exiting."
  exit 1
fi
echo "Install Baetyl..."
install_baetyl
echo "Install k9s..."
install_k9s
echo "Save Device ID..."
save_device_id
sudo rm -rf /home/<USER>/setup.sh
sleep 30
echo "Rebooting..."
sudo reboot