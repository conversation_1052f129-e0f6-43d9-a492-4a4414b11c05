include:
  - project: "delivery/longclawx01/mercedes/cloud-mono-repo"
    ref: "main"
    file: "gitlab/gitlab-ci.template.yml"

stages:
  - build
  - deploy

.fleet_management:
  variables:
    APPLICATION: fleet-management
    BUILD_ARG: --platform linux/amd64 --build-arg RELEASE_VERSION=2.4.3

build_dev:
  extends:
    - .fleet_management
    - .build
    - .dev

deploy_dev:
  extends:
    - .fleet_management
    - .deploy
    - .dev
  needs:
    - job: build_dev
      artifacts: true

build_staging:
  extends:
    - .fleet_management
    - .build
    - .staging
    - .manual_trigger

deploly_staging:
  extends:
    - .fleet_management
    - .deploy
    - .staging
  needs:
    - job: build_staging
      artifacts: true

build_spearow:
  extends:
    - .fleet_management
    - .build
    - .spearow
    - .manual_trigger

deploy_spearow:
  extends:
    - .fleet_management
    - .deploy
    - .spearow
  needs:
    - job: build_spearow
      artifacts: true

build_prod:
  extends:
    - .fleet_management
    - .build
    - .prod
    - .manual_trigger

deploy_prod:
  extends:
    - .fleet_management
    - .deploy
    - .prod
  needs:
    - job: build_prod
      artifacts: true