workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "main"
      variables:
        PIPELINE_NAME: CI
        ENVIRONMENT: development
    - if: $CI_COMMIT_BRANCH && $CI_OPEN_MERGE_REQUESTS
      variables:
        PIPELINE_NAME: CI
        ENVIRONMENT: development
    - if: $CI_COMMIT_REF_NAME == 'main'
      variables:
        PIPELINE_NAME: CD
        ENVIRONMENT: development
    - if: $CI_COMMIT_TAG
      variables:
        PIPELINE_NAME: CD
        ENVIRONMENT: production
# ##
#     - if: $CI_COMMIT_REF_NAME == "update-ci-2"
#       variables:
#         PIPELINE_NAME: CI
#         ENVIRONMENT: test
# ##
    - when: never

variables:
  AWS_REGION: "ap-southeast-1"

default:
  interruptible: true

stages:
  - build
  - deploy  
  - security-scan
  - code-quality
  - dependency-check
  - container-scan
  - test

# Before script
.install_aws_cli_before_script: &install_aws_cli_before_script
  - |
    apk update \
    && apk add --no-cache git tzdata make gettext bash curl jq unzip python3-dev py3-pip aws-cli

.configure_aws_cli_before_script: &configure_aws_cli_before_script
  - aws configure set aws_access_key_id "${AWS_ACCESS_KEY_ID}"
  - aws configure set aws_secret_access_key "${AWS_SECRET_ACCESS_KEY}"
  - aws configure set default.region "${AWS_REGION}"
  - aws configure set default.output json
  - aws sts get-caller-identity

.lint-web:
  image: node:20-alpine
  stage: code-quality
  script:
    - npm install
    - npm run lint --format json --output-file lint-report.json
  allow_failure: true
  artifacts:
    paths:
      - lint-report.json
  rules:
  - if: $PIPELINE_NAME == "CI" && $ENVIRONMENT == "development"
  - when: never

.semgrep-sast:
  image: registry.gitlab.com/gitlab-org/security-products/analyzers/semgrep:latest
  stage: security-scan
  variables:
    SEARCH_MAX_DEPTH: 20
  allow_failure: true
  script:
    - semgrep --config=auto --dryrun --json > semgrep-report.json
  artifacts:
    reports:
      sast: semgrep-report.json
    paths:
      - semgrep-report.json
  rules:
  - if: $PIPELINE_NAME == "CI" && $ENVIRONMENT == "development"
  - when: never
  tags:
    - generic-runner

.dependency-check-nodejs:
  image:
    name: node:18-alpine
  stage: dependency-check
  script:
    - npm install
    - npm audit --json > npm-audit-report.json 
  allow_failure: true
  artifacts:
    paths:
    - npm-audit-report.json
  rules:
  - if: $PIPELINE_NAME == "CI" && $ENVIRONMENT == "development"
  - when: never

.govulncheck:
  stage: dependency-check
  image: golang:1.22-alpine  # Use official Go image
  allow_failure: true
  script:
    - go install golang.org/x/vuln/cmd/govulncheck@latest
    - govulncheck -json ./... > govulncheck-report.json
  artifacts:
    paths:
      - govulncheck-report.json
  rules:
  - if: $PIPELINE_NAME == "CI" && $ENVIRONMENT == "development"
  - when: never

.gosec:
  image: golang:1.22-alpine
  stage:  security-scan
  allow_failure: true
  before_script:
  - apk add curl
  - curl -sfL https://raw.githubusercontent.com/securego/gosec/master/install.sh | sh -s -- -b $(go env GOPATH)/bin v2.15.0
  script:
  - gosec -fmt sonarqube -out gosec-sonarqube-report.json -stdout ./...
  artifacts:
    paths:
    - gosec-sonarqube-report.json
  rules:
  - if: $PIPELINE_NAME == "CI" && $ENVIRONMENT == "development"
  - when: never

.golint:
  image: golangci/golangci-lint:v1.61-alpine
  stage: code-quality
  allow_failure: true
  before_script:
  - apk update && apk add jq
  script:
  - golangci-lint run --timeout 5m0s --enable=errcheck --enable=govet --enable=staticcheck --enable=unused --enable=gocyclo --out-format json ./... > gl-code-quality-sonarqube.json
  artifacts:
    paths:
    - gl-code-quality-sonarqube.json
  rules:
  - if: $PIPELINE_NAME == "CI" && $ENVIRONMENT == "development"
  - when: never

# Build image with Docker and push to ECR
.build:
  image: docker:27.5.1
  stage: build
  allow_failure: false
  when: on_success
  variables:
    DOCKER_HOST: tcp://docker:2376
    DOCKER_TLS_CERTDIR: "/certs"
    DOCKER_TLS_VERIFY: 1
    DOCKER_CERT_PATH: "${DOCKER_TLS_CERTDIR}/client"
    GIT_TERMINAL_PROMPT: 1
    AWS_ECR: ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com
    REPOSITORY_NAME: ${AWS_ECR}/${APPLICATION}
    DOCKERFILE: "Dockerfile"
  services:
    - docker:27.5.1-dind
  before_script:
    - *install_aws_cli_before_script
    - *configure_aws_cli_before_script
    - aws ecr get-login-password --region ${AWS_REGION} | docker login --username AWS --password-stdin ${AWS_ECR}
    - docker image prune -f
    - docker buildx ls
    - docker pull ${REPOSITORY_NAME}:${LATEST_TAG} || echo "Docker image cache is unavailable, skip using caching layer."
  script:
    - |
      echo "Building docker image ${REPOSITORY_NAME}"
      DOCKER_BUILDKIT=1 docker buildx build -f ${DOCKERFILE} --cache-from=${REPOSITORY_NAME}:${LATEST_TAG} -t ${REPOSITORY_NAME}:${TAG_NAME} -t ${REPOSITORY_NAME}:${LATEST_TAG} ${BUILD_ARG} .
    - |
      echo "Pushing docker image ${REPOSITORY_NAME} to registry"
      docker push ${REPOSITORY_NAME} --all-tags
    - echo "REPOSITORY_NAME=${REPOSITORY_NAME}"
    - echo "APPLICATION=${APPLICATION}"
    - echo "${REPOSITORY_NAME}:${TAG_NAME}"

# Job to scan the Docker image using Trivy
.container-scan:
  stage: container-scan
  image:
    name: aquasec/trivy:latest
    entrypoint: [""]
  variables:
    DOCKER_HOST: tcp://docker:2376
    DOCKER_TLS_CERTDIR: "/certs"
    DOCKER_TLS_VERIFY: 1
    DOCKER_CERT_PATH: "${DOCKER_TLS_CERTDIR}/client"
    GIT_TERMINAL_PROMPT: 1
    AWS_ECR: ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com
    REPOSITORY_NAME: ${AWS_ECR}/${APPLICATION}
  before_script:
    - *install_aws_cli_before_script
    - *configure_aws_cli_before_script
    - apk add --no-cache docker
    - aws ecr get-login-password --region ${AWS_REGION} | docker login --username AWS --password-stdin ${AWS_ECR}
  script:
    - trivy --version
    - trivy image --exit-code 1 --severity HIGH,CRITICAL ${REPOSITORY_NAME}:${LATEST_TAG}
  allow_failure: true

# Deploy to EKS Cluster with ArgoCD
.deploy:
  image: alpine:3.18
  stage: deploy
  allow_failure: false
  when: on_success
  variables:
    AWS_ECR: ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com
    REPOSITORY_NAME: ${AWS_ECR}/${APPLICATION} 
  before_script: 
    - |
      apk update \
      && apk add --no-cache git tzdata make gettext bash curl yq jq unzip ca-certificates
    - | 
      echo "Add gitlab.knizsoft.com certificates"
      cp /etc/gitlab-runner/certs/*.crt /usr/local/share/ca-certificates/ \
      && cp /certs/client/*.crt /usr/local/share/ca-certificates/ \
      && update-ca-certificates --fresh > /dev/null
  script:
    - |
      echo "Deploy with new image: $REPOSITORY_NAME:$TAG_NAME"
    - git -c http.sslVerify=false clone https://oauth2:$<EMAIL>/delivery/longclawx01/mercedes/cloud-mono-repo.git
    - cd cloud-mono-repo
    - git checkout main
    - bash scripts/update-image-tag.sh

.manual_trigger:
  when: manual

.gdev:
  variables:
    AWS_ACCOUNT_ID: "************"
    KUSTOMIZE_ENV: dev
    AWS_ACCESS_KEY_ID: $DEV_AWS_ACCESS_KEY_ID
    AWS_SECRET_ACCESS_KEY: $DEV_AWS_SECRET_ACCESS_KEY
  rules:
    - if: $PIPELINE_NAME == "CD" && $ENVIRONMENT == "development"
      variables:
        TAG_NAME: "${CI_COMMIT_SHORT_SHA}"
        LATEST_TAG: "latest"
    - when: never

.dev:
  extends:
    - .gdev
  tags:
    - mve2-dev-runner

.dev_orin:
  extends:
    - .gdev
  tags:
    - mve2-dev-orin-runner

.gstaging:
  variables:
    AWS_ACCOUNT_ID: "************"
    KUSTOMIZE_ENV: staging
    AWS_ACCESS_KEY_ID: $STAGING_AWS_ACCESS_KEY_ID
    AWS_SECRET_ACCESS_KEY: $STAGING_AWS_SECRET_ACCESS_KEY
  rules:
    - if: $PIPELINE_NAME == "CD" && $ENVIRONMENT == "development"
      variables:
        TAG_NAME: "${CI_COMMIT_SHORT_SHA}stg"
        LATEST_TAG: "staging"
    - when: never

.staging:
  extends:
    - .gstaging
  tags:
    - mve2-staging-runner

.staging_orin:
  extends:
    - .gstaging
  tags:
    - mve2-staging-orin-runner

.gspearow:
  variables:
    AWS_ACCOUNT_ID: "************"
    KUSTOMIZE_ENV: spearow
    AWS_ACCESS_KEY_ID: $SPEAROW_AWS_ACCESS_KEY_ID
    AWS_SECRET_ACCESS_KEY: $SPEAROW_AWS_SECRET_ACCESS_KEY
  rules:
    - if: $PIPELINE_NAME == "CD" && $ENVIRONMENT == "production"
      variables:
        TAG_NAME: "${CI_COMMIT_TAG}"
        LATEST_TAG: "production"
    - when: never

.spearow:
  extends:
    - .gspearow
  tags:
    - mve2-spearow-runner

.spearow_orin:
  extends:
    - .gspearow
  tags:
    - mve2-spearow-orin-runner

.gprod:
  variables:
    AWS_ACCOUNT_ID: "************"
    KUSTOMIZE_ENV: prod
    AWS_ACCESS_KEY_ID: $PROD_AWS_ACCESS_KEY_ID
    AWS_SECRET_ACCESS_KEY: $PROD_AWS_SECRET_ACCESS_KEY
  rules:
    - if: $PIPELINE_NAME == "CD" && $ENVIRONMENT == "production"
      variables:
        TAG_NAME: "${CI_COMMIT_TAG}"
        LATEST_TAG: "production"
    - when: never

.prod:
  extends:
    - .gprod
  tags:
    - mve2-prod-runner

.prod_orin:
  extends:
    - .gprod
  tags:
    - mve2-prod-orin-runner
