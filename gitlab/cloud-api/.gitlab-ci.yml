include:
  - project: "delivery/longclawx01/mercedes/cloud-mono-repo"
    ref: "main"
    # ref: "update-ci"
    file: "gitlab/gitlab-ci.template.yml"

stages:
  - ci
  - build
  - deploy
  - security-scan
  - code-quality
  - dependency-check
  - container-scan
  - test

.cloud_api:
  variables:
    APPLICATION: cloud-api
    BUILD_ARG: "--platform linux/amd64 --build-arg COMMIT=${CI_COMMIT_SHA}"

golint:
  extends:
    - .dev
    - .cloud_api
    - .golint

gosec:
  extends:
    - .cloud_api
    - .dev
    - .gosec 

security-scan:
  extends: 
    - .semgrep-sast

dependency-check:
  extends:
    - .cloud_api
    - .dev
    - .govulncheck

build_dev:
  extends:
    - .cloud_api
    - .build
    - .dev

scan-dev:
  extends: 
    - .dev
    - .cloud_api
    - .container-scan
  needs:
    - job: build_dev
      artifacts: true

deploy_dev:
  extends:
    - .cloud_api
    - .deploy
    - .dev
  needs:
    - job: build_dev
      artifacts: true

build_staging:
  extends:
    - .cloud_api
    - .build
    - .staging
    - .manual_trigger

deploy_staging:
  extends:
    - .cloud_api
    - .deploy
    - .staging
  needs:
    - job: build_staging
      artifacts: true

build_spearow:
  extends:
    - .cloud_api
    - .build
    - .spearow
    - .manual_trigger

deploy_spearow:
  extends:
    - .cloud_api
    - .deploy
    - .spearow
  needs:
    - job: build_spearow
      artifacts: true

build_prod:
  extends:
    - .cloud_api
    - .build
    - .prod
    - .manual_trigger

deploy_prod:
  extends:
    - .cloud_api
    - .deploy
    - .prod
  needs:
    - job: build_prod
      artifacts: true