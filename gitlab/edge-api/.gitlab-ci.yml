include:
  - project: "delivery/longclawx01/mercedes/cloud-mono-repo"
    ref: "main"
    # ref: "update-ci"
    file: "gitlab/gitlab-ci.template.yml"

stages:
  - ci
  - build
  - deploy
  - security-scan
  - code-quality
  - dependency-check
  - container-scan
  - test

.edge_api:
  variables:
    APPLICATION: edge-api
    BUILD_ARG: --platform linux/arm64 --build-arg COMMIT=${CI_COMMIT_SHA}

golint:
  extends:
    - .dev_orin
    - .edge_api
    - .golint

gosec:
  extends:
    - .edge_api
    - .dev_orin
    - .gosec 

security-scan:
  extends: 
    - .semgrep-sast

dependency-check:
  extends:
    - .edge_api
    - .dev_orin
    - .govulncheck

build_dev:
  extends:
    - .edge_api
    - .build
    - .dev_orin

scan-dev:
  extends: 
    - .dev_orin
    - .edge_api
    - .container-scan
  needs:
    - job: build_dev
      artifacts: true

build_staging:
  extends:
    - .edge_api
    - .build
    - .staging_orin
    - .manual_trigger

build_spearow:
  extends:
    - .edge_api
    - .build
    - .spearow_orin
    - .manual_trigger

build_prod:
  extends:
    - .edge_api
    - .build
    - .prod_orin
    - .manual_trigger