include:
  - project: "delivery/longclawx01/mercedes/cloud-mono-repo"
    ref: "main"
    file: "gitlab/gitlab-ci.template.yml"

stages:
  - build

.dev_variables:
  variables:
    AWS_ACCOUNT_ID: "************"
    KUSTOMIZE_ENV: dev
    AWS_ACCESS_KEY_ID: $DEV_AWS_ACCESS_KEY_ID
    AWS_SECRET_ACCESS_KEY: $DEV_AWS_SECRET_ACCESS_KEY

.staging_variables:
  variables:
    AWS_ACCOUNT_ID: "************"
    KUSTOMIZE_ENV: staging
    AWS_ACCESS_KEY_ID: $STAGING_AWS_ACCESS_KEY_ID
    AWS_SECRET_ACCESS_KEY: $STAGING_AWS_SECRET_ACCESS_KEY

.spearow_variables:
  variables:
    AWS_ACCOUNT_ID: "************"
    KUSTOMIZE_ENV: spearow
    AWS_ACCESS_KEY_ID: $SPEAROW_AWS_ACCESS_KEY_ID
    AWS_SECRET_ACCESS_KEY: $SPEAROW_AWS_SECRET_ACCESS_KEY

.prod_variables:
  variables:
    AWS_ACCOUNT_ID: "************"
    KUSTOMIZE_ENV: prod
    AWS_ACCESS_KEY_ID: $PROD_AWS_ACCESS_KEY_ID
    AWS_SECRET_ACCESS_KEY: $PROD_AWS_SECRET_ACCESS_KEY

.edge_vaengine:
  variables:
    APPLICATION: edge-vaengine
    RUNNER_SCRIPT_TIMEOUT: 2h # Increase timeout since prod and staging have 1hr timeout and unable to build

# Build image for dev
build_dev_35_5:
  variables:
    TAG_NAME: ${CI_COMMIT_SHORT_SHA}_dev_35_5
    LATEST_TAG: dev_35_5
    DOCKERFILE: "35_5_0.Dockerfile"
  extends:
    - .edge_vaengine
    - .build
    - .dev_variables
  tags:
    - mve2-dev-orin-runner

build_dev_36_4:
  variables:
    TAG_NAME: ${CI_COMMIT_SHORT_SHA}_dev_36_4
    LATEST_TAG: dev_36_4
    DOCKERFILE: "36_4_0.Dockerfile"
  extends:
    - .edge_vaengine
    - .build
    - .dev_variables
  tags:
    - mve2-dev-orin-runner

# Build image for staging
build_staging_35_5:
  variables:
    TAG_NAME: ${CI_COMMIT_SHORT_SHA}_staging_35_5
    LATEST_TAG: staging_35_5
    DOCKERFILE: "35_5_0.Dockerfile"
  extends:
    - .edge_vaengine
    - .build
    - .staging_variables
    - .manual_trigger
  tags:
    - mve2-staging-orin-runner
    
build_staging_36_4:
  variables:
    TAG_NAME: ${CI_COMMIT_SHORT_SHA}_staging_36_4
    LATEST_TAG: staging_36_4
    DOCKERFILE: "36_4_0.Dockerfile"
  extends:
    - .edge_vaengine
    - .build
    - .staging_variables
    - .manual_trigger
  tags:
    - mve2-staging-orin-runner

# Build image for spearow   
build_spearow_35_5:
  variables:
    TAG_NAME: ${CI_COMMIT_SHORT_SHA}_spearow_35_5
    LATEST_TAG: spearow_35_5
    DOCKERFILE: "35_5_0.Dockerfile"
  extends:
    - .edge_vaengine
    - .build
    - .spearow_variables
    - .manual_trigger
  tags:
    - mve2-spearow-orin-runner

build_spearow_36_4:
  variables:
    TAG_NAME: ${CI_COMMIT_SHORT_SHA}_spearow_36_4
    LATEST_TAG: spearow_36_4
    DOCKERFILE: "36_4_0.Dockerfile"
  extends:
    - .edge_vaengine
    - .build
    - .spearow_variables
    - .manual_trigger
  tags:
    - mve2-spearow-orin-runner

# Build image for prod
build_prod_35_5:
  variables:
    TAG_NAME: ${CI_COMMIT_SHORT_SHA}_prod_35_5
    LATEST_TAG: prod_35_5
    DOCKERFILE: "35_5_0.Dockerfile"
  extends:
    - .edge_vaengine
    - .build
    - .prod_variables
    - .manual_trigger
  tags:
    - mve2-prod-orin-runner

build_prod_36_4:
  variables:
    TAG_NAME: ${CI_COMMIT_SHORT_SHA}_prod_36_4
    LATEST_TAG: prod_36_4
    DOCKERFILE: "36_4_0.Dockerfile"
  extends:
    - .edge_vaengine
    - .build
    - .prod_variables
    - .manual_trigger
  tags:
    - mve2-prod-orin-runner
