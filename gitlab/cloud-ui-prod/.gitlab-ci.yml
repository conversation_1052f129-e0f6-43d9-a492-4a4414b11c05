include:
  - project: "delivery/longclawx01/mercedes/cloud-mono-repo"
    ref: "main"
    # ref: "update-cicd-pipeline"
    file: "gitlab/gitlab-ci.template.yml"

stages:
  - build
  - deploy
  - container-scan
  - dependency-check
  - security-scan
  - code-quality

.cloud_ui:
  variables:
    APPLICATION: cloud-ui
    BUILD_ARG: "--platform linux/amd64 --build-arg ENDPOINT=${API_ENDPOINT} --build-arg SOCKET=${SOCKET_ENDPOINT} --build-arg GRAFANA_PANEL_URL=${GRAFANA_PANEL_ENDPOINT}"

lint:
  extends: 
    - .cloud_ui
    - .dev
    - .lint-web

security-scan:
  extends: 
    - .semgrep-sast

dependency-check:
  extends:
    - .cloud_ui
    - .dev
    - .dependency-check-nodejs

build_prod:
  extends:
    - .cloud_ui
    - .build
    - .prod
    - .manual_trigger
  variables:
    API_ENDPOINT: https://api.mve2-prod.knovel.org/api/v1
    SOCKET_ENDPOINT: wss://api.mve2-prod.knovel.org/socket.io
    GRAFANA_PANEL_ENDPOINT: https://grafana.mve2-prod.knovel.org/d/bfff402a-80e3-4651-afd6-53a0d8144e83/edge-device-logs?orgId=1

container-scan-prod:
  extends: 
    - .prod
    - .cloud_ui
    - .container-scan
  needs:
    - job: build_prod
      artifacts: true

deploy_prod:
  extends:
    - .cloud_ui
    - .deploy
    - .prod
  needs:
    - job: build_prod
      artifacts: true
