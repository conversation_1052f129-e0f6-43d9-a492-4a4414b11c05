include:
  - project: "delivery/longclawx01/mercedes/cloud-mono-repo"
    ref: "main"
    # ref: "update-security-scan"
    file: "gitlab/gitlab-ci.template.yml"

stages:
  - build
  - deploy  
  - security-scan
  - code-quality
  - dependency-check
  - container-scan
  - test

.edge_ui:
  variables:
    APPLICATION: edge-ui
    BUILD_ARG: --platform linux/arm64 --build-arg ENDPOINT=http://localhost:30081/api/v1 --build-arg SOCKET=http://localhost:30081

lint:
  extends: 
    - .edge_ui
    - .dev_orin
    - .lint-web

security-scan:
  extends: 
    - .semgrep-sast

dependency-check:
  extends:
    - .edge_ui
    - .dev_orin
    - .dependency-check-nodejs

build_prod:
  extends:
    - .edge_ui
    - .build
    - .prod_orin
    - .manual_trigger

container-scan-prod:
  extends: 
    - .prod_orin
    - .edge_ui
    - .container-scan
  needs:
    - job: build_prod
      artifacts: true

