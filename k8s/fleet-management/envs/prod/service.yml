apiVersion: v1
kind: Service
metadata:
  name: fleet-management-sync
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-name: mve2-fleet-sync-prod # Name of the NLB
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: tcp
    service.beta.kubernetes.io/aws-load-balancer-scheme: internet-facing
    service.beta.kubernetes.io/aws-load-balancer-cross-zone-load-balancing-enabled: "true"
    service.beta.kubernetes.io/aws-load-balancer-nlb-target-type: ip
    service.beta.kubernetes.io/aws-load-balancer-ip-address-type: dualstack
    service.beta.kubernetes.io/aws-load-balancer-alpn-policy: "HTTP2Preferred"
spec:
  type: LoadBalancer
  externalTrafficPolicy: Local
  ports:
    - name: sync-port
      port: 443
      targetPort: 9005
      protocol: TCP
