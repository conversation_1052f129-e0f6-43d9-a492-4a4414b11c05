apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
namespace: baetyl-cloud
commonLabels:
  app.kubernetes.io/environment: staging #CHANGE_FOR_DIFF_ENV
generatorOptions:
  disableNameSuffixHash: true
resources:
  - ../../base
  - route-ingress.yml
  - service.yml
configMapGenerator:
  - name: fleet-management-template-config
    files:
      - templates/baetyl-init-deployment.yml
    behavior: merge
images:
  - name: "fleet-management"
    newName: "572881485767.dkr.ecr.ap-southeast-1.amazonaws.com/fleet-management" #CHANGE_IF_NECESSARY
    newTag: "a2505e35"
