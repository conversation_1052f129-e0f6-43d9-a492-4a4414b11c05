---
apiVersion: v1
kind: Namespace
metadata:
  name: {{.EdgeSystemNamespace}}
---
apiVersion: v1
kind: Namespace
metadata:
  name: {{.EdgeNamespace}}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: ecr-helper-sa
  namespace: {{.EdgeNamespace}}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: {{.EdgeNamespace}}
  name: ecr-helper-role
rules:
  - apiGroups: [""]
    resources: ["secrets"]
    resourceNames: ["privateecr"]
    verbs: ["delete"]
  - apiGroups: [""]
    resources: ["secrets"]
    verbs: ["create"]
---
kind: RoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: ecr-helper-role-binding
  namespace: {{.EdgeNamespace}}
subjects:
  - kind: ServiceAccount
    name: ecr-helper-sa
    namespace: {{.EdgeNamespace}}
    apiGroup: ""
roleRef:
  kind: Role
  name: ecr-helper-role
  apiGroup: ""
---
apiVersion: v1
kind: Secret
type: Opaque
metadata:
  name: ecr-helper-secret
  namespace: {{.EdgeNamespace}}
stringData:
  AWS_ACCESS_KEY_ID: ******************** #CHANGE_IF_NECESSARY
  AWS_SECRET_ACCESS_KEY: YNxf10s0DSY3RS2Xz4lSy0JcsIgggpX+P6LtSluA #CHANGE_IF_NECESSARY
  AWS_ACCOUNT: "************" #CHANGE_IF_NECESSARY
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: ecr-helper-configmap
  namespace: {{.EdgeNamespace}}
data:
  AWS_REGION: "ap-southeast-1"
  DOCKER_SECRET_NAME: privateecr
  APP_NAMESPACE: {{.EdgeNamespace}}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: ecr-helper-script-configmap
  namespace: {{.EdgeNamespace}}
data:
  scripts.sh: |-
    #!/bin/sh
    aws sts get-caller-identity
    until ECR_TOKEN=$(aws ecr get-login-password --region ${AWS_REGION}); do
      echo "Failed to get ECR token, retrying in 5 seconds..."
      sleep 5
    done
    until kubectl delete secret --ignore-not-found $DOCKER_SECRET_NAME -n $APP_NAMESPACE; do
      echo "Failed to delete secret, retrying in 5 seconds..."
      sleep 5
    done
    until kubectl create secret docker-registry $DOCKER_SECRET_NAME \
    --docker-server=https://${AWS_ACCOUNT}.dkr.ecr.${AWS_REGION}.amazonaws.com \
    --docker-username=AWS \
    --docker-password="${ECR_TOKEN}" \
    --namespace=$APP_NAMESPACE; do
      echo "Failed to create secret, retrying in 5 seconds..."
      sleep 5
    done
    echo "Secret was successfully updated at $(date)"

---
apiVersion: batch/v1
kind: Job
metadata:
  name: ecr-helper
  namespace: {{.EdgeNamespace}}
spec:
  template:
    spec:
      serviceAccountName: ecr-helper-sa
      containers:
        - name: aws-cli
          image: ducmeit1/aws-kubectl:latest
          imagePullPolicy: IfNotPresent
          envFrom:
            - secretRef:
                name: ecr-helper-secret
            - configMapRef:
                name: ecr-helper-configmap
          volumeMounts:
            - name: script-volume
              mountPath: /tmp/scripts.sh
              subPath: scripts.sh
          command:
            - /bin/sh
            - -c
            - /tmp/scripts.sh

      volumes:
        - name: script-volume
          configMap:
            name: ecr-helper-script-configmap
            defaultMode: 0755
      restartPolicy: OnFailure
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: ecr-helper
  namespace: {{.EdgeNamespace}}
spec:
  schedule: "0 */10 * * *"
  successfulJobsHistoryLimit: 3
  suspend: false
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: ecr-helper-sa
          containers:
            - name: aws-cli
              image: ducmeit1/aws-kubectl:latest
              imagePullPolicy: IfNotPresent
              envFrom:
                - secretRef:
                    name: ecr-helper-secret
                - configMapRef:
                    name: ecr-helper-configmap
              volumeMounts:
                - name: script-volume
                  mountPath: /tmp/scripts.sh
                  subPath: scripts.sh
              command:
                - /bin/sh
                - -c
                - /tmp/scripts.sh
          volumes:
            - name: script-volume
              configMap:
                name: ecr-helper-script-configmap
                defaultMode: 0755
          restartPolicy: OnFailure
---
#MV-Edge
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: local-path
provisioner: rancher.io/local-path
reclaimPolicy: Delete
volumeBindingMode: WaitForFirstConsumer
---
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: local-path-provisioner-service-account
  namespace: {{.EdgeNamespace}}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: mve-api
  namespace: {{.EdgeNamespace}}
---
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: mve-api-cluster-role-binding
  namespace: {{.EdgeNamespace}}
subjects:
  - kind: ServiceAccount
    name: mve-api
    namespace: {{.EdgeNamespace}}
roleRef:
  kind: ClusterRole
  name: cluster-admin
  apiGroup: rbac.authorization.k8s.io

---
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: mve-frontend
  namespace: {{.EdgeNamespace}}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: mve-stream
  namespace: {{.EdgeNamespace}}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: mve-worker
  namespace: {{.EdgeNamespace}}
---
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: mve-worker-cluster-role-binding
  namespace: {{.EdgeNamespace}}
subjects:
  - kind: ServiceAccount
    name: mve-worker
    namespace: {{.EdgeNamespace}}
roleRef:
  kind: ClusterRole
  name: cluster-admin
  apiGroup: rbac.authorization.k8s.io
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: local-path-provisioner-role
rules:
  - apiGroups:
      - ""
    resources:
      - nodes
      - persistentvolumeclaims
      - configmaps
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - ""
    resources:
      - endpoints
      - persistentvolumes
      - pods
    verbs:
      - "*"
  - apiGroups:
      - ""
    resources:
      - events
    verbs:
      - create
      - patch
  - apiGroups:
      - storage.k8s.io
    resources:
      - storageclasses
    verbs:
      - get
      - list
      - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: local-path-provisioner-bind
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: local-path-provisioner-role
subjects:
  - kind: ServiceAccount
    name: local-path-provisioner-service-account
    namespace: {{.EdgeNamespace}}
---
apiVersion: v1
data:
  config.json: |-
    {
            "nodePathMap": [],
            "sharedFileSystemPath": "/shared/fs"
    }
  helperPod.yaml: |-
    apiVersion: v1
    kind: Pod
    metadata:
      name: helper-pod
    spec:
      containers:
      - name: helper-pod
        image: busybox
        imagePullPolicy: IfNotPresent
  setup: |-
    #!/bin/sh
    set -eu
    mkdir -m 0777 -p "$VOL_DIR"
  teardown: |-
    #!/bin/sh
    set -eu
    rm -rf "$VOL_DIR"
kind: ConfigMap
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: local-path-config
  namespace: {{.EdgeNamespace}}
---
apiVersion: v1
data:
  DEBUG: "false"
  IMAGES_ROOT_PATH: /mnt/data/images
  SQLITE_AUTO_MIGRATE: "false"
  SQLITE_CONNECTION_STRING: /mnt/database/prod.db?cache=shared&mode=rwc&_fk=1&_journal_mode=WAL
  SQLITE_MIGRATION_PATH: database/migrations
  STREAM_HTTP_ENDPOINT: /live
  STREAM_RTMP_ENDPOINT: rtmp://mve-stream.baetyl-edge.svc:1935/live
  VA_CONFIG_PATH: /mnt/config/va_config.json
  VA_ENGINE_ID: {{.NodeName}}
  VIDEOS_ROOT_PATH: /mnt/data/videos
  RECORDING_COMMAND_ROOT_PATH: /mnt/data/record-command
  APPLICATION_LOCAL_ROOT_PATH: /mnt/data/application-temp
  CLOUD_URL: https://api.mve2-staging.knizsoft.com/
  REDIS_URL: "mve-redis:6379"
  REDIS_CONSUMER_NAME: "consumer-1"
  REDIS_NOTIFICATION_STREAM_NAME: "notification"
kind: ConfigMap
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: mve-api-configmap
  namespace: {{.EdgeNamespace}}
---
apiVersion: v1
data:
  VITE_API_ENDPOINT: http://localhost:30081/api/v1
  VITE_APP_WEBSOCKET_URL: http://localhost:30081
  VITE_APP_FOOTER: "Powered by Knizsoft"
kind: ConfigMap
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: mve-frontend-configmap
  namespace: {{.EdgeNamespace}}
---
apiVersion: v1
data:
  srs.conf: |
    listen              1935;
    max_connections     1000;
    daemon              off;
    http_api {
        enabled         on;
        listen          1985;
    }
    http_server {
        enabled         on;
        listen          8080;
    }
    vhost __defaultVhost__ {
        http_remux {
            enabled     on;
        }
        hls {
            enabled         on;
            hls_dispose     30;
        }
    }
kind: ConfigMap
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: mve-stream-configmap
  namespace: {{.EdgeNamespace}}
---
apiVersion: v1
data:
  DEBUG: "false"
  IMAGES_ROOT_PATH: /mnt/data/images
  MQTT_BROKER: mqtt.mve2-staging.knizsoft.com #CHANGE_IF_NOT_USING_KNOVEL #CHANGE_FOR_DIFF_ENV
  MQTT_PORT: "8883"
  PRESIGNED_API: "https://api.mve2-staging.knizsoft.com/api/v1/devices" #CHANGE_IF_NOT_USING_KNOVEL #CHANGE_FOR_DIFF_ENV
  SQLITE_AUTO_MIGRATE: "false"
  SQLITE_CONNECTION_STRING: /mnt/database/prod.db?cache=shared&mode=rwc&_fk=1&_journal_mode=WAL
  SQLITE_MIGRATION_PATH: database/migrations
  VA_ENGINE_ID: {{.NodeName}}
  VIDEOS_ROOT_PATH: /mnt/data/videos
  MQTT_STAT_TOPIC: "stats"
  MQTT_DETECTION_TOPIC: "detection"
  MQTT_CA_FILE : "/mnt/mqtt-cert/server-ca.crt"
  MQTT_CERT_FILE: "/mnt/mqtt-cert/client.crt"
  MQTT_KEY_FILE: "/mnt/mqtt-cert/client.key"
  MQTT_TIMESYNC_STAT_TOPIC: "3"
  MQTT_TIMESYNC_DETECTION_TOPIC: "10"
  BAETYL_NAMESPACE: "baetyl-edge-system"
  BAETYL_CORE_DEPLOYMENT_NAME: "baetyl-core"
  REDIS_URL: "mve-redis:6379"
  REDIS_DETECTION_STREAM_NAME: "detection"
  REDIS_NOTIFICATION_STREAM_NAME: "notification"
  REDIS_CONSUMER_NAME: "consumer-1"
  REDIS_TRACK_EXPIRE_TIME_IN_MINUTES: "1"
  CLEAN_STORAGE_TIME_TO_KEEP_VIDEO_IN_HOURS: "48"
  CLEAN_STORAGE_TIME_TO_KEEP_DETECTION_IN_HOURS: "48"
  CLEAN_STORAGE_TIME_CLEAN_IN_HOURS: "24"
  CLEAN_STORAGE_STORAGE_THRESHOLD: "70"
  RESTREAM_COMMAND_TEMPLATE: |
    "ffmpeg -f v4l2 -i %s -c:v libx264 -c:a aac -preset ultrafast -tune zerolatency -b:v 2M -r 15 -buffer_size 5M -f rtsp %s/%s"
kind: ConfigMap
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: mve-worker-configmap
  namespace: {{.EdgeNamespace}}
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: mve-api
  namespace: {{.EdgeNamespace}}
spec:
  ports:
    - name: http
      port: 8000
      nodePort: 30081
      protocol: TCP
      targetPort: http
  selector:
    app.kubernetes.io/component: api
    app.kubernetes.io/instance: mve-api
    app.kubernetes.io/name: mve-api
    app.kubernetes.io/version: 1.0.0
  type: NodePort
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: mve-frontend
  namespace: {{.EdgeNamespace}}
spec:
  ports:
    - name: http
      port: 80
      nodePort: 30080
      protocol: TCP
      targetPort: http
  selector:
    app.kubernetes.io/component: frontend
    app.kubernetes.io/instance: mve-frontend
    app.kubernetes.io/name: mve-frontend
    app.kubernetes.io/version: 1.0.0
  type: NodePort
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: mve-stream
  namespace: {{.EdgeNamespace}}
spec:
  ports:
    - name: http
      port: 8080
      nodePort: 30085
      protocol: TCP
      targetPort: http
    - name: api
      port: 1985
      nodePort: 31985
      protocol: TCP
      targetPort: api
    - name: rtmp
      port: 1935
      nodePort: 31935
      protocol: TCP
      targetPort: rtmp
  selector:
    app.kubernetes.io/component: stream
    app.kubernetes.io/instance: mve-stream
    app.kubernetes.io/name: mve-stream
    app.kubernetes.io/version: 1.0.0
  type: NodePort
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: mve-worker
  namespace: {{.EdgeNamespace}}
spec:
  ports:
    - name: http
      port: 8000
      protocol: TCP
      targetPort: http
  selector:
    app.kubernetes.io/component: worker
    app.kubernetes.io/instance: mve-worker
    app.kubernetes.io/name: mve-worker
    app.kubernetes.io/version: 1.0.0
  type: ClusterIP
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: api-data-pvc
  namespace: {{.EdgeNamespace}}
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1800Gi
  storageClassName: local-path
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: api-database-pvc
  namespace: {{.EdgeNamespace}}
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
  storageClassName: local-path
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: va-config-pvc
  namespace: {{.EdgeNamespace}}
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 1Gi
  storageClassName: local-path
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: local-path-provisioner
  namespace: {{.EdgeNamespace}}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: local-path-provisioner
      app.kubernetes.io/version: 1.0.0
  template:
    metadata:
      labels:
        app: local-path-provisioner
        app.kubernetes.io/part-of: mve
        app.kubernetes.io/version: 1.0.0
    spec:
      containers:
        - command:
            - local-path-provisioner
            - --debug
            - start
            - --config
            - /etc/config/config.json
          env:
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          image: rancher/local-path-provisioner:v0.0.24
          imagePullPolicy: IfNotPresent
          name: local-path-provisioner
          volumeMounts:
            - mountPath: /etc/config/
              name: config-volume
      serviceAccountName: local-path-provisioner-service-account
      volumes:
        - configMap:
            name: local-path-config
          name: config-volume
---
apiVersion: v1
kind: Secret
metadata:
  name: mve-worker-secrets
  namespace: {{.EdgeNamespace}}
type: Opaque
data: #CHANGE_FOR_DIFF_ENV #CHANGE_IF_NOT_USING_KNOVEL 
  server-ca.crt: "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"
  client.key: "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
  client.crt: "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"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: mve-api
  namespace: {{.EdgeNamespace}}
spec:
  replicas: 1
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app.kubernetes.io/component: api
      app.kubernetes.io/instance: mve-api
      app.kubernetes.io/name: mve-api
      app.kubernetes.io/version: 1.0.0
  strategy:
    rollingUpdate:
      maxSurge: 100%
      maxUnavailable: 10%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app.kubernetes.io/component: api
        app.kubernetes.io/instance: mve-api
        app.kubernetes.io/name: mve-api
        app.kubernetes.io/part-of: mve
        app.kubernetes.io/version: 1.0.0
    spec:
      containers:
        - args:
            - api
          command:
            - /app/server
          env:
            - name: K8S_POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: K8S_NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
          envFrom:
            - configMapRef:
                name: mve-api-configmap
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/edge-api:staging #CHANGE_IF_NECESSARY
          securityContext:
            privileged: true
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /ready/liveliness
              port: http
            initialDelaySeconds: 3
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 5
          name: api
          ports:
            - containerPort: 8000
              name: http
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /ready/readiness
              port: http
            initialDelaySeconds: 3
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: "2"
              memory: 4Gi
            requests:
              cpu: "1"
              memory: 2Gi
          volumeMounts:
            - mountPath: /mnt/data
              name: data
            - mountPath: /mnt/database
              name: database
            - mountPath: /mnt/config
              name: config
      imagePullSecrets:
        - name: privateecr
      securityContext: {}
      serviceAccountName: mve-api
      volumes:
        - hostPath:
            path: /data/cluster_data/data
            type: DirectoryOrCreate
          name: data
        - hostPath:
            path: /data/cluster_data/database
            type: DirectoryOrCreate
          name: database
        - hostPath:
            path: /data/cluster_data/config
            type: DirectoryOrCreate
          name: config
        - emptyDir: {}
          name: tmp-dir
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: mve-frontend
  namespace: {{.EdgeNamespace}}
spec:
  replicas: 1
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app.kubernetes.io/component: frontend
      app.kubernetes.io/instance: mve-frontend
      app.kubernetes.io/name: mve-frontend
      app.kubernetes.io/version: 1.0.0
  strategy:
    rollingUpdate:
      maxSurge: 100%
      maxUnavailable: 10%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app.kubernetes.io/component: frontend
        app.kubernetes.io/instance: mve-frontend
        app.kubernetes.io/name: mve-frontend
        app.kubernetes.io/part-of: mve
        app.kubernetes.io/version: 1.0.0
    spec:
      containers:
        - env:
            - name: K8S_POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: K8S_NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
          envFrom:
            - configMapRef:
                name: mve-frontend-configmap
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/edge-ui:staging #CHANGE_IF_NECESSARY
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /
              port: http
            initialDelaySeconds: 3
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 5
          name: web
          ports:
            - containerPort: 80
              name: http
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /
              port: http
            initialDelaySeconds: 3
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: "1"
              memory: 2Gi
            requests:
              cpu: 128m
              memory: 256Mi
      imagePullSecrets:
        - name: privateecr
      securityContext: {}
      serviceAccountName: mve-frontend
      volumes:
        - emptyDir: {}
          name: tmp-dir
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: mve-stream
  namespace: {{.EdgeNamespace}}
spec:
  replicas: 1
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app.kubernetes.io/component: stream
      app.kubernetes.io/instance: mve-stream
      app.kubernetes.io/name: mve-stream
      app.kubernetes.io/version: 1.0.0
  strategy:
    rollingUpdate:
      maxSurge: 100%
      maxUnavailable: 10%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app.kubernetes.io/component: stream
        app.kubernetes.io/instance: mve-stream
        app.kubernetes.io/name: mve-stream
        app.kubernetes.io/part-of: mve
        app.kubernetes.io/version: 1.0.0
    spec:
      containers:
        - env:
            - name: K8S_POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: K8S_NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
          image: ossrs/srs:v6
          imagePullPolicy: IfNotPresent
          command:
            - ./objs/srs
            - -c
            - conf/docker.conf
          name: stream
          ports:
            - containerPort: 8080
              name: http
              protocol: TCP
            - containerPort: 1985
              name: api
              protocol: TCP
            - containerPort: 1935
              name: rtmp
              protocol: TCP
          resources:
            limits:
              cpu: "4"
              memory: 8Gi
            requests:
              cpu: "1"
              memory: 2Gi
      imagePullSecrets: []
      securityContext: {}
      serviceAccountName: mve-stream
      volumes:
        - emptyDir: {}
          name: tmp-dir
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: mve-worker
  namespace: {{.EdgeNamespace}}
spec:
  replicas: 1
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app.kubernetes.io/component: worker
      app.kubernetes.io/instance: mve-worker
      app.kubernetes.io/name: mve-worker
      app.kubernetes.io/version: 1.0.0
  strategy:
    rollingUpdate:
      maxSurge: 100%
      maxUnavailable: 10%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app.kubernetes.io/component: worker
        app.kubernetes.io/instance: mve-worker
        app.kubernetes.io/name: mve-worker
        app.kubernetes.io/part-of: mve
        app.kubernetes.io/version: 1.0.0
    spec:
      containers:
        - args:
            - worker
          command:
            - /app/server
          env:
            - name: K8S_POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: K8S_NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
          envFrom:
            - configMapRef:
                name: mve-worker-configmap
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/edge-api:staging #CHANGE_IF_NECESSARY
          securityContext:
            privileged: true
          imagePullPolicy: IfNotPresent
          name: worker
          resources:
            limits:
              cpu: "2"
              memory: 4Gi
            requests:
              cpu: "1"
              memory: 2Gi
          volumeMounts:
            - mountPath: /mnt/data
              name: data
            - mountPath: /mnt/database
              name: database
            - mountPath: /mnt/mqtt-cert
              name: mqtt-cert
              readOnly: true
            - mountPath: /sysrq
              name: proc
      imagePullSecrets:
        - name: privateecr
      securityContext: {}
      serviceAccountName: mve-worker
      volumes:
        - hostPath:
            path: /data/cluster_data/data
            type: DirectoryOrCreate
          name: data
        - hostPath:
            path: /data/cluster_data/database
            type: DirectoryOrCreate
          name: database
        - hostPath:
            path:  /proc/sysrq-trigger
            type: File
          name: proc
        - name: mqtt-cert
          secret:
            secretName: mve-worker-secrets
        - emptyDir: {}
          name: tmp-dir
---
#Baetyl agent
apiVersion: v1
kind: ServiceAccount
metadata:
  name: baetyl-edge-system-service-account
  namespace: {{.EdgeSystemNamespace}}

---
# elevation of authority
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: baetyl-edge-system-rbac
subjects:
  - kind: ServiceAccount
    name: baetyl-edge-system-service-account
    namespace: {{.EdgeSystemNamespace}}
roleRef:
  kind: ClusterRole
  name: cluster-admin
  apiGroup: rbac.authorization.k8s.io

---
apiVersion: v1
kind: Secret
metadata:
  name: {{.NodeCertName}}
  namespace: {{.EdgeSystemNamespace}}
type: Opaque
data:
  client.pem: "{{.NodeCertPem}}"
  client.key: "{{.NodeCertKey}}"
  ca.pem: "{{.NodeCertCa}}"

---
# baetyl-init configmap
apiVersion: v1
kind: ConfigMap
metadata:
  name: baetyl-init-config
  namespace: {{.EdgeSystemNamespace}}
data:
  conf.yml: |-
    node:
      ca: var/lib/baetyl/node/ca.pem
      key: var/lib/baetyl/node/client.key
      cert: var/lib/baetyl/node/client.pem
    sync:
      download:
        timeout: 30m
    httplink:
      address: "{{GetProperty "sync-server-address"}}"
      insecureSkipVerify: true
    logger:
      level: debug
      encoding: console

---
# baetyl-init deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: baetyl-init
  namespace: {{.EdgeSystemNamespace}}
  labels:
    baetyl-app-name: "{{.InitAppName}}"
    baetyl-app-version: "{{.InitVersion}}"
    baetyl-service-name: baetyl-init
spec:
  selector:
    matchLabels:
      baetyl-service-name: baetyl-init
  replicas: 1
  template:
    metadata:
      labels:
        baetyl-app-name: baetyl-init
        baetyl-service-name: baetyl-init
    spec:
      # nodeName: {{.KubeNodeName}}
      tolerations:
        - key: node-role.kubernetes.io/master
          operator: Exists
          effect: NoSchedule
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: node-role.kubernetes.io/master
                    operator: Exists
      serviceAccountName: baetyl-edge-system-service-account
      containers:
        - name: baetyl-init
          image: {{GetModuleImage "baetyl"}}
          imagePullPolicy: IfNotPresent
          args:
            - init
          env:
            - name: BAETYL_APP_NAME
              value: "{{.InitAppName}}"
            - name: BAETYL_APP_VERSION
              value: "{{.InitVersion}}"
            - name: BAETYL_NODE_NAME
              value: "{{.NodeName}}"
            - name: BAETYL_SERVICE_NAME
              value: "baetyl-init"
            - name: BAETYL_RUN_MODE
              value: "kube"
            - name: KUBE_NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
          securityContext:
            privileged: true
          volumeMounts:
            - name: init-conf
              mountPath: /etc/baetyl
            - name: core-store-path
              mountPath: /var/lib/baetyl/store
            - name: object-download-path
              mountPath: /var/lib/baetyl/object
            - name: host-root-path
              mountPath: /var/lib/baetyl/host
            - name: node-cert
              mountPath: var/lib/baetyl/node
      volumes:
        - name: init-conf
          configMap:
            name: baetyl-init-config
        - name: core-store-path
          hostPath:
            path: /var/lib/baetyl/store
        - name: object-download-path
          hostPath:
            path: /var/lib/baetyl/object
        - name: host-root-path
          hostPath:
            path: /var/lib/baetyl/host
        - name: node-cert
          secret:
            secretName: {{.NodeCertName}}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: fluent-bit
  namespace: kube-system
  labels:
    app.kubernetes.io/name: fluent-bit
    app.kubernetes.io/instance: fluent-bit
    app.kubernetes.io/version: "2.1.4"
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: fluent-bit
  namespace: kube-system
  labels:
    app.kubernetes.io/name: fluent-bit
    app.kubernetes.io/instance: fluent-bit
    app.kubernetes.io/version: "2.1.4"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: fluent-bit
  namespace: kube-system
  labels:
    app.kubernetes.io/name: fluent-bit
    app.kubernetes.io/instance: fluent-bit
    app.kubernetes.io/version: "2.1.4"
data: #CHANGE_IF_NOT_USING_KNOVEL #CHANGE_FOR_DIFF_ENV (below [OUTPUT] host and http_passwd)
  labelmap.json: |
    {
      "kubernetes": {
        "container_name": "container",
        "labels": {
          "app.kubernetes.io/instance": "app",
          "app.kubernetes.io/version": "version"
        },
        "namespace_name": "namespace",
        "pod_name": "instance"
      },
      "level": "level"
    }
  fluent-bit-worker-ota.conf: |
    [INPUT]
        Name tail
        Path /var/log/worker-ota-syslog
        Tag worker-ota
        Mem_Buf_Limit 5MB
        Buffer_Chunk_size 32k
        Buffer_Max_size 32k
    [FILTER]
        Name grep
        Match worker-ota
        Regex log .*what.*
    [OUTPUT]
        Name loki
        Match worker-ota
        Host loki.mve2-staging.knizsoft.com
        Port 443
        http_user loki
        http_passwd ZRug2fOqnvJ21l93
        TLS on
        TLS.Verify off
        Labels device={{.NodeName}}, app=worker-ota, version=1.0.0, level=INFO
  fluent-bit.conf: |
    [SERVICE]
        Daemon Off
        Flush 1
        Log_Level info
        Parsers_File parsers.conf
        HTTP_Server On
        HTTP_Listen 0.0.0.0
        HTTP_Port 2020
        Health_Check On

    [INPUT]
        Name tail
        Path /var/log/containers/*.log
        Tag kube.*
        multiline.parser go, docker, cri
        DB /run/fluent-bit/flb_kube.db
        Mem_Buf_Limit 5MB
        Mem_Buf_Limit     5MB
        Buffer_Chunk_size 32k
        Buffer_Max_size   32k

    [FILTER]
        Name kubernetes
        Match kube.*
        Kube_URL https://kubernetes.default.svc:443
        Merge_Log On
        Keep_Log Off
        K8S-Logging.Parser On
        K8S-Logging.Exclude Off

    [FILTER]
        Name           grep
        Match          kube.*
        Logical_Op     or
        Exclude        $kubernetes['namespace_name'] baetyl-edge-system
        Exclude        $kubernetes['namespace_name'] kube-system
        Exclude        $kubernetes['namespace_name'] default
        Exclude        $level DEBUG
        Exclude        $kubernetes['container_name'] stream
        Exclude        $kubernetes['container_name'] web

    [OUTPUT]
        name loki
        match kube.*
        host loki.mve2-staging.knizsoft.com
        port 443
        http_user loki
        http_passwd ZRug2fOqnvJ21l93
        tls on
        tls.verify off
        labels device={{.NodeName}}
        drop_single_key true
        remove_keys kubernetes,stream
        auto_kubernetes_labels off
        label_map_path /fluent-bit/etc/labelmap.json
        line_format json

    @INCLUDE fluent-bit-worker-ota.conf
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: fluent-bit
  labels:
    app.kubernetes.io/name: fluent-bit
    app.kubernetes.io/instance: fluent-bit
    app.kubernetes.io/version: "2.1.4"
rules:
  - apiGroups:
      - ""
    resources:
      - namespaces
      - pods
    verbs:
      - get
      - list
      - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: fluent-bit
  labels:
    app.kubernetes.io/name: fluent-bit
    app.kubernetes.io/instance: fluent-bit
    app.kubernetes.io/version: "2.1.4"
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: fluent-bit
subjects:
  - kind: ServiceAccount
    name: fluent-bit
    namespace: kube-system
---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: fluent-bit
  namespace: kube-system
  labels:
    app.kubernetes.io/name: fluent-bit
    app.kubernetes.io/instance: fluent-bit
    app.kubernetes.io/version: "2.1.4"
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: fluent-bit
      app.kubernetes.io/instance: fluent-bit
  template:
    metadata:
      annotations:
        checksum/config: e9e9111ee63554f1f5428424f30c7083181fa0e7d625381e1835efe5e3ed3def
        checksum/luascripts: e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855
      labels:
        app.kubernetes.io/name: fluent-bit
        app.kubernetes.io/instance: fluent-bit
    spec:
      serviceAccountName: fluent-bit
      hostNetwork: false
      dnsPolicy: ClusterFirst
      containers:
        - name: fluent-bit
          image: "cr.fluentbit.io/fluent/fluent-bit:2.1.4"
          imagePullPolicy: IfNotPresent
          securityContext:
            privileged: true
          ports:
            - name: http
              containerPort: 2020
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /
              port: http
          readinessProbe:
            httpGet:
              path: /api/v1/health
              port: http
          volumeMounts:
            - mountPath: /fluent-bit/etc/fluent-bit-worker-ota.conf
              name: config
              subPath: fluent-bit-worker-ota.conf
            - mountPath: /fluent-bit/etc/fluent-bit.conf
              name: config
              subPath: fluent-bit.conf
            - mountPath: /fluent-bit/etc/labelmap.json
              name: config
              subPath: labelmap.json
            - mountPath: /var/log/worker-ota-syslog
              name: varlogsyslog
              readOnly: true
            - mountPath: /var/log/containers
              name: varlogcontainers
              readOnly: true
            - mountPath: /var/log/pods
              name: varlogpods
              readOnly: true
            - mountPath: /var/lib/docker/containers
              name: varlibdockercontainers
              readOnly: true
            - mountPath: /data/docker/containers
              name: datadockercontainers
              readOnly: true
            - name: run
              mountPath: /run/fluent-bit
      volumes:
        - name: config
          configMap:
            name: fluent-bit
        - hostPath:
            path: /var/log/syslog
            type: File
          name: varlogsyslog
        - hostPath:
            path: /var/log/containers
          name: varlogcontainers
        - hostPath:
            path: /var/log/pods
          name: varlogpods
        - hostPath:
            path: /var/lib/docker/containers
          name: varlibdockercontainers
        - hostPath:
            path: /data/docker/containers
          name: datadockercontainers
        - name: run
          hostPath:
            path: /run/fluent-bit
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: mve-redis
  namespace: {{.EdgeNamespace}}
spec:
  ports:
    - protocol: TCP
      port: 6379
      targetPort: 6379
      nodePort: 30079
  selector:
    app.kubernetes.io/component: redis
    app.kubernetes.io/instance: mve-redis
    app.kubernetes.io/name: mve-redis
    app.kubernetes.io/version: 1.0.0
  type: NodePort
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: mve-redis
  namespace: {{.EdgeNamespace}}
spec:
  replicas: 1
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app.kubernetes.io/component: redis
      app.kubernetes.io/instance: mve-redis
      app.kubernetes.io/name: mve-redis
      app.kubernetes.io/version: 1.0.0
  strategy:
    rollingUpdate:
      maxSurge: 100%
      maxUnavailable: 10%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app.kubernetes.io/component: redis
        app.kubernetes.io/instance: mve-redis
        app.kubernetes.io/name: mve-redis
        app.kubernetes.io/part-of: mve
        app.kubernetes.io/version: 1.0.0
    spec:
      volumes:
        - name: redis-persistence
          emptyDir:
            medium: "Memory"
      containers:
      - name: redis
        image: arm64v8/redis:7.0
        ports:
        - containerPort: 6379
        volumeMounts:
        - name: redis-persistence
          mountPath: /data
        resources:
          requests:
            memory: "64Mi"
            cpu: "250m"
          limits:
            memory: "128Mi"
            cpu: "500m"
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: mve-rtsp-server
  namespace: {{.EdgeNamespace}}
spec:
  ports:
    - name: http
      port: 8554
      nodePort: 30054
      protocol: TCP
      targetPort: http
  selector:
    app.kubernetes.io/component: rtsp-server
    app.kubernetes.io/instance: mve-rtsp-server
    app.kubernetes.io/name: mve-rtsp-server
    app.kubernetes.io/version: 1.0.0
  type: NodePort

---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: mve-rtsp-server
  namespace: {{.EdgeNamespace}}
spec:
  replicas: 1
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app.kubernetes.io/component: rtsp-server
      app.kubernetes.io/instance: mve-rtsp-server
      app.kubernetes.io/name: mve-rtsp-server
      app.kubernetes.io/version: 1.0.0
  strategy:
    rollingUpdate:
      maxSurge: 100%
      maxUnavailable: 10%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app.kubernetes.io/component: rtsp-server
        app.kubernetes.io/instance: mve-rtsp-server
        app.kubernetes.io/name: mve-rtsp-server
        app.kubernetes.io/part-of: mve
        app.kubernetes.io/version: 1.0.0
    spec:
      containers:
        - env:
            - name: K8S_POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: K8S_NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: MTX_PROTOCOLS
              value: tcp
          image: aler9/rtsp-simple-server:v1.10.0
          imagePullPolicy: IfNotPresent
          name: rtsp-server
          ports:
            - containerPort: 8554
              name: http
              protocol: TCP

          resources:
            limits:
              cpu: "4"
              memory: 8Gi
            requests:
              cpu: "1"
              memory: 2Gi
      imagePullSecrets: []
      securityContext: {}
      volumes:
        - emptyDir: {}
          name: tmp-dir