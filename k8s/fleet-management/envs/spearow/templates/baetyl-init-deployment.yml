---
apiVersion: v1
kind: Namespace
metadata:
  name: {{.EdgeSystemNamespace}}
---
apiVersion: v1
kind: Namespace
metadata:
  name: {{.EdgeNamespace}}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: ecr-helper-sa
  namespace: {{.EdgeNamespace}}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: {{.EdgeNamespace}}
  name: ecr-helper-role
rules:
  - apiGroups: [""]
    resources: ["secrets"]
    resourceNames: ["privateecr"]
    verbs: ["delete"]
  - apiGroups: [""]
    resources: ["secrets"]
    verbs: ["create"]
---
kind: RoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: ecr-helper-role-binding
  namespace: {{.EdgeNamespace}}
subjects:
  - kind: ServiceAccount
    name: ecr-helper-sa
    namespace: {{.EdgeNamespace}}
    apiGroup: ""
roleRef:
  kind: Role
  name: ecr-helper-role
  apiGroup: ""
---
apiVersion: v1
kind: Secret
type: Opaque
metadata:
  name: ecr-helper-secret
  namespace: {{.EdgeNamespace}}
stringData:
  AWS_ACCESS_KEY_ID: ********************
  AWS_SECRET_ACCESS_KEY: m80kmjirQ787nDm7cxYXq6neestaPKy2hIZOcPZX
  AWS_ACCOUNT: "************"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: ecr-helper-configmap
  namespace: {{.EdgeNamespace}}
data:
  AWS_REGION: "ap-southeast-1"
  DOCKER_SECRET_NAME: privateecr
  APP_NAMESPACE: {{.EdgeNamespace}}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: ecr-helper-script-configmap
  namespace: {{.EdgeNamespace}}
data:
  scripts.sh: |-
    #!/bin/sh
    aws sts get-caller-identity
    ECR_TOKEN=`aws ecr get-login-password --region ${AWS_REGION}`
    kubectl delete secret --ignore-not-found $DOCKER_SECRET_NAME -n $APP_NAMESPACE
    kubectl create secret docker-registry $DOCKER_SECRET_NAME \
    --docker-server=https://${AWS_ACCOUNT}.dkr.ecr.${AWS_REGION}.amazonaws.com \
    --docker-username=AWS \
    --docker-password="${ECR_TOKEN}" \
    --namespace=$APP_NAMESPACE
    echo "Secret was successfully updated at $(date)"

---
apiVersion: batch/v1
kind: Job
metadata:
  name: ecr-helper
  namespace: {{.EdgeNamespace}}
spec:
  template:
    spec:
      serviceAccountName: ecr-helper-sa
      containers:
        - name: aws-cli
          image: ducmeit1/aws-kubectl:latest
          imagePullPolicy: IfNotPresent
          envFrom:
            - secretRef:
                name: ecr-helper-secret
            - configMapRef:
                name: ecr-helper-configmap
          volumeMounts:
            - name: script-volume
              mountPath: /tmp/scripts.sh
              subPath: scripts.sh
          command:
            - /bin/sh
            - -c
            - /tmp/scripts.sh

      volumes:
        - name: script-volume
          configMap:
            name: ecr-helper-script-configmap
            defaultMode: 0755
      restartPolicy: OnFailure
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: ecr-helper
  namespace: {{.EdgeNamespace}}
spec:
  schedule: "0 */10 * * *"
  successfulJobsHistoryLimit: 3
  suspend: false
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: ecr-helper-sa
          containers:
            - name: aws-cli
              image: ducmeit1/aws-kubectl:latest
              imagePullPolicy: IfNotPresent
              envFrom:
                - secretRef:
                    name: ecr-helper-secret
                - configMapRef:
                    name: ecr-helper-configmap
              volumeMounts:
                - name: script-volume
                  mountPath: /tmp/scripts.sh
                  subPath: scripts.sh
              command:
                - /bin/sh
                - -c
                - /tmp/scripts.sh
          volumes:
            - name: script-volume
              configMap:
                name: ecr-helper-script-configmap
                defaultMode: 0755
          restartPolicy: OnFailure
---
#MV-Edge
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: local-path
provisioner: rancher.io/local-path
reclaimPolicy: Delete
volumeBindingMode: WaitForFirstConsumer
---
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: local-path-provisioner-service-account
  namespace: {{.EdgeNamespace}}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: mve-api
  namespace: {{.EdgeNamespace}}
---
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: mve-api-cluster-role-binding
  namespace: {{.EdgeNamespace}}
subjects:
  - kind: ServiceAccount
    name: mve-api
    namespace: {{.EdgeNamespace}}
roleRef:
  kind: ClusterRole
  name: cluster-admin
  apiGroup: rbac.authorization.k8s.io

---
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: mve-frontend
  namespace: {{.EdgeNamespace}}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: mve-stream
  namespace: {{.EdgeNamespace}}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: mve-worker
  namespace: {{.EdgeNamespace}}
---
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: mve-worker-cluster-role-binding
  namespace: {{.EdgeNamespace}}
subjects:
  - kind: ServiceAccount
    name: mve-worker
    namespace: {{.EdgeNamespace}}
roleRef:
  kind: ClusterRole
  name: cluster-admin
  apiGroup: rbac.authorization.k8s.io
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: local-path-provisioner-role
rules:
  - apiGroups:
      - ""
    resources:
      - nodes
      - persistentvolumeclaims
      - configmaps
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - ""
    resources:
      - endpoints
      - persistentvolumes
      - pods
    verbs:
      - "*"
  - apiGroups:
      - ""
    resources:
      - events
    verbs:
      - create
      - patch
  - apiGroups:
      - storage.k8s.io
    resources:
      - storageclasses
    verbs:
      - get
      - list
      - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: local-path-provisioner-bind
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: local-path-provisioner-role
subjects:
  - kind: ServiceAccount
    name: local-path-provisioner-service-account
    namespace: {{.EdgeNamespace}}
---
apiVersion: v1
data:
  config.json: |-
    {
            "nodePathMap": [],
            "sharedFileSystemPath": "/shared/fs"
    }
  helperPod.yaml: |-
    apiVersion: v1
    kind: Pod
    metadata:
      name: helper-pod
    spec:
      containers:
      - name: helper-pod
        image: busybox
        imagePullPolicy: IfNotPresent
  setup: |-
    #!/bin/sh
    set -eu
    mkdir -m 0777 -p "$VOL_DIR"
  teardown: |-
    #!/bin/sh
    set -eu
    rm -rf "$VOL_DIR"
kind: ConfigMap
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: local-path-config
  namespace: {{.EdgeNamespace}}
---
apiVersion: v1
data:
  DEBUG: "false"
  IMAGES_ROOT_PATH: /mnt/data/images
  SQLITE_AUTO_MIGRATE: "false"
  SQLITE_CONNECTION_STRING: /mnt/database/prod.db?cache=shared&mode=rwc&_fk=1&_journal_mode=WAL
  SQLITE_MIGRATION_PATH: database/migrations
  STREAM_HTTP_ENDPOINT: /live
  STREAM_RTMP_ENDPOINT: rtmp://mve-stream.baetyl-edge.svc:1935/live
  VA_CONFIG_PATH: /mnt/config/va_config.json
  VA_ENGINE_ID: {{.NodeName}}
  VIDEOS_ROOT_PATH: /mnt/data/videos
  RECORDING_COMMAND_ROOT_PATH: /mnt/data/record-command
  APPLICATION_LOCAL_ROOT_PATH: /mnt/data/application-temp
  CLOUD_URL: https://api.mve2-spearow.knovel.org/
  REDIS_URL: "mve-redis:6379"
  REDIS_CONSUMER_NAME: "consumer-1"
  REDIS_NOTIFICATION_STREAM_NAME: "notification"
  TZ: "Asia/Singapore"
kind: ConfigMap
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: mve-api-configmap
  namespace: {{.EdgeNamespace}}
---
apiVersion: v1
data:
  VITE_API_ENDPOINT: http://localhost:30081/api/v1
  VITE_APP_WEBSOCKET_URL: http://localhost:30081
  VITE_APP_FOOTER: "Powered by Knizsoft"
kind: ConfigMap
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: mve-frontend-configmap
  namespace: {{.EdgeNamespace}}
---
apiVersion: v1
data:
  srs.conf: |
    listen              1935;
    max_connections     1000;
    daemon              off;
    http_api {
        enabled         on;
        listen          1985;
    }
    http_server {
        enabled         on;
        listen          8080;
    }
    vhost __defaultVhost__ {
        http_remux {
            enabled     on;
        }
        hls {
            enabled         on;
            hls_dispose     30;
        }
    }
kind: ConfigMap
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: mve-stream-configmap
  namespace: {{.EdgeNamespace}}
---
apiVersion: v1
data:
  DEBUG: "false"
  IMAGES_ROOT_PATH: /mnt/data/images
  MQTT_BROKER: mqtt.mve2-spearow.knovel.org
  MQTT_PORT: "8883"
  PRESIGNED_API: "https://api.mve2-spearow.knovel.org/api/v1/devices"
  SQLITE_AUTO_MIGRATE: "false"
  SQLITE_CONNECTION_STRING: /mnt/database/prod.db?cache=shared&mode=rwc&_fk=1&_journal_mode=WAL
  SQLITE_MIGRATION_PATH: database/migrations
  VA_ENGINE_ID: {{.NodeName}}
  VIDEOS_ROOT_PATH: /mnt/data/videos
  MQTT_STAT_TOPIC: "stats"
  MQTT_DETECTION_TOPIC: "detection"
  MQTT_CA_FILE : "/mnt/mqtt-cert/server-ca.crt"
  MQTT_CERT_FILE: "/mnt/mqtt-cert/client.crt"
  MQTT_KEY_FILE: "/mnt/mqtt-cert/client.key"
  MQTT_TIMESYNC_STAT_TOPIC: "3"
  MQTT_TIMESYNC_DETECTION_TOPIC: "10"
  BAETYL_NAMESPACE: "baetyl-edge-system"
  BAETYL_CORE_DEPLOYMENT_NAME: "baetyl-core"
  REDIS_URL: "mve-redis:6379"
  REDIS_DETECTION_STREAM_NAME: "detection"
  REDIS_NOTIFICATION_STREAM_NAME: "notification"
  REDIS_CONSUMER_NAME: "consumer-1"
  REDIS_TRACK_EXPIRE_TIME_IN_MINUTES: "1"
  CLEAN_STORAGE_TIME_TO_KEEP_VIDEO_IN_HOURS: "48"
  CLEAN_STORAGE_TIME_TO_KEEP_DETECTION_IN_HOURS: "48"
  CLEAN_STORAGE_TIME_CLEAN_IN_HOURS: "24"
  CLEAN_STORAGE_STORAGE_THRESHOLD: "70"
  RESTREAM_COMMAND_TEMPLATE: |
    "ffmpeg -f v4l2 -i %s -c:v libx264 -c:a aac -preset ultrafast -tune zerolatency -b:v 2M -r 15 -buffer_size 5M -f rtsp %s/%s"
  TZ: "Asia/Singapore"
kind: ConfigMap
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: mve-worker-configmap
  namespace: {{.EdgeNamespace}}
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: mve-api
  namespace: {{.EdgeNamespace}}
spec:
  ports:
    - name: http
      port: 8000
      nodePort: 30081
      protocol: TCP
      targetPort: http
  selector:
    app.kubernetes.io/component: api
    app.kubernetes.io/instance: mve-api
    app.kubernetes.io/name: mve-api
    app.kubernetes.io/version: 1.0.0
  type: NodePort
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: mve-frontend
  namespace: {{.EdgeNamespace}}
spec:
  ports:
    - name: http
      port: 80
      nodePort: 30080
      protocol: TCP
      targetPort: http
  selector:
    app.kubernetes.io/component: frontend
    app.kubernetes.io/instance: mve-frontend
    app.kubernetes.io/name: mve-frontend
    app.kubernetes.io/version: 1.0.0
  type: NodePort
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: mve-stream
  namespace: {{.EdgeNamespace}}
spec:
  ports:
    - name: http
      port: 8080
      nodePort: 30085
      protocol: TCP
      targetPort: http
    - name: api
      port: 1985
      nodePort: 31985
      protocol: TCP
      targetPort: api
    - name: rtmp
      port: 1935
      nodePort: 31935
      protocol: TCP
      targetPort: rtmp
  selector:
    app.kubernetes.io/component: stream
    app.kubernetes.io/instance: mve-stream
    app.kubernetes.io/name: mve-stream
    app.kubernetes.io/version: 1.0.0
  type: NodePort
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: mve-worker
  namespace: {{.EdgeNamespace}}
spec:
  ports:
    - name: http
      port: 8000
      protocol: TCP
      targetPort: http
  selector:
    app.kubernetes.io/component: worker
    app.kubernetes.io/instance: mve-worker
    app.kubernetes.io/name: mve-worker
    app.kubernetes.io/version: 1.0.0
  type: ClusterIP
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: api-data-pvc
  namespace: {{.EdgeNamespace}}
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1800Gi
  storageClassName: local-path
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: api-database-pvc
  namespace: {{.EdgeNamespace}}
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
  storageClassName: local-path
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: va-config-pvc
  namespace: {{.EdgeNamespace}}
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 1Gi
  storageClassName: local-path
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: local-path-provisioner
  namespace: {{.EdgeNamespace}}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: local-path-provisioner
      app.kubernetes.io/version: 1.0.0
  template:
    metadata:
      labels:
        app: local-path-provisioner
        app.kubernetes.io/part-of: mve
        app.kubernetes.io/version: 1.0.0
    spec:
      containers:
        - command:
            - local-path-provisioner
            - --debug
            - start
            - --config
            - /etc/config/config.json
          env:
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          image: rancher/local-path-provisioner:v0.0.24
          imagePullPolicy: IfNotPresent
          name: local-path-provisioner
          volumeMounts:
            - mountPath: /etc/config/
              name: config-volume
      serviceAccountName: local-path-provisioner-service-account
      volumes:
        - configMap:
            name: local-path-config
          name: config-volume
---
apiVersion: v1
kind: Secret
metadata:
  name: mve-worker-secrets
  namespace: {{.EdgeNamespace}}
type: Opaque
data:
  server-ca.crt: "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"
  client.key: "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
  client.crt: "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"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: mve-api
  namespace: {{.EdgeNamespace}}
spec:
  replicas: 1
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app.kubernetes.io/component: api
      app.kubernetes.io/instance: mve-api
      app.kubernetes.io/name: mve-api
      app.kubernetes.io/version: 1.0.0
  strategy:
    rollingUpdate:
      maxSurge: 100%
      maxUnavailable: 10%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app.kubernetes.io/component: api
        app.kubernetes.io/instance: mve-api
        app.kubernetes.io/name: mve-api
        app.kubernetes.io/part-of: mve
        app.kubernetes.io/version: 1.0.0
    spec:
      containers:
        - args:
            - api
          command:
            - /app/server
          env:
            - name: K8S_POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: K8S_NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
          envFrom:
            - configMapRef:
                name: mve-api-configmap
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/edge-api:production
          securityContext:
            privileged: true
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /ready/liveliness
              port: http
            initialDelaySeconds: 3
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 5
          name: api
          ports:
            - containerPort: 8000
              name: http
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /ready/readiness
              port: http
            initialDelaySeconds: 3
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: "2"
              memory: 4Gi
            requests:
              cpu: "1"
              memory: 2Gi
          volumeMounts:
            - mountPath: /mnt/data
              name: data
            - mountPath: /mnt/database
              name: database
            - mountPath: /mnt/config
              name: config
      imagePullSecrets:
        - name: privateecr
      securityContext: {}
      serviceAccountName: mve-api
      volumes:
        - hostPath:
            path: /data/cluster_data/data
            type: DirectoryOrCreate
          name: data
        - hostPath:
            path: /data/cluster_data/database
            type: DirectoryOrCreate
          name: database
        - hostPath:
            path: /data/cluster_data/config
            type: DirectoryOrCreate
          name: config
        - emptyDir: {}
          name: tmp-dir
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: mve-frontend
  namespace: {{.EdgeNamespace}}
spec:
  replicas: 1
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app.kubernetes.io/component: frontend
      app.kubernetes.io/instance: mve-frontend
      app.kubernetes.io/name: mve-frontend
      app.kubernetes.io/version: 1.0.0
  strategy:
    rollingUpdate:
      maxSurge: 100%
      maxUnavailable: 10%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app.kubernetes.io/component: frontend
        app.kubernetes.io/instance: mve-frontend
        app.kubernetes.io/name: mve-frontend
        app.kubernetes.io/part-of: mve
        app.kubernetes.io/version: 1.0.0
    spec:
      containers:
        - env:
            - name: K8S_POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: K8S_NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
          envFrom:
            - configMapRef:
                name: mve-frontend-configmap
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/edge-ui:production
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /
              port: http
            initialDelaySeconds: 3
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 5
          name: web
          ports:
            - containerPort: 80
              name: http
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /
              port: http
            initialDelaySeconds: 3
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: "1"
              memory: 2Gi
            requests:
              cpu: 128m
              memory: 256Mi
      imagePullSecrets:
        - name: privateecr
      securityContext: {}
      serviceAccountName: mve-frontend
      volumes:
        - emptyDir: {}
          name: tmp-dir
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: mve-stream
  namespace: {{.EdgeNamespace}}
spec:
  replicas: 1
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app.kubernetes.io/component: stream
      app.kubernetes.io/instance: mve-stream
      app.kubernetes.io/name: mve-stream
      app.kubernetes.io/version: 1.0.0
  strategy:
    rollingUpdate:
      maxSurge: 100%
      maxUnavailable: 10%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app.kubernetes.io/component: stream
        app.kubernetes.io/instance: mve-stream
        app.kubernetes.io/name: mve-stream
        app.kubernetes.io/part-of: mve
        app.kubernetes.io/version: 1.0.0
    spec:
      containers:
        - env:
            - name: K8S_POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: K8S_NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
          image: ossrs/srs:v6
          imagePullPolicy: IfNotPresent
          command:
            - ./objs/srs
            - -c
            - conf/docker.conf
          name: stream
          ports:
            - containerPort: 8080
              name: http
              protocol: TCP
            - containerPort: 1985
              name: api
              protocol: TCP
            - containerPort: 1935
              name: rtmp
              protocol: TCP
          resources:
            limits:
              cpu: "4"
              memory: 8Gi
            requests:
              cpu: "1"
              memory: 2Gi
      imagePullSecrets: []
      securityContext: {}
      serviceAccountName: mve-stream
      volumes:
        - emptyDir: {}
          name: tmp-dir
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: mve-worker
  namespace: {{.EdgeNamespace}}
spec:
  replicas: 1
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app.kubernetes.io/component: worker
      app.kubernetes.io/instance: mve-worker
      app.kubernetes.io/name: mve-worker
      app.kubernetes.io/version: 1.0.0
  strategy:
    rollingUpdate:
      maxSurge: 100%
      maxUnavailable: 10%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app.kubernetes.io/component: worker
        app.kubernetes.io/instance: mve-worker
        app.kubernetes.io/name: mve-worker
        app.kubernetes.io/part-of: mve
        app.kubernetes.io/version: 1.0.0
    spec:
      containers:
        - args:
            - worker
          command:
            - /app/server
          env:
            - name: K8S_POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: K8S_NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
          envFrom:
            - configMapRef:
                name: mve-worker-configmap
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/edge-api:production
          securityContext:
            privileged: true
          imagePullPolicy: IfNotPresent
          name: worker
          resources:
            limits:
              cpu: "2"
              memory: 4Gi
            requests:
              cpu: "1"
              memory: 2Gi
          volumeMounts:
            - mountPath: /mnt/data
              name: data
            - mountPath: /mnt/database
              name: database
            - mountPath: /mnt/mqtt-cert
              name: mqtt-cert
              readOnly: true
            - mountPath: /sysrq
              name: proc
      imagePullSecrets:
        - name: privateecr
      securityContext: {}
      serviceAccountName: mve-worker
      volumes:
        - hostPath:
            path: /data/cluster_data/data
            type: DirectoryOrCreate
          name: data
        - hostPath:
            path: /data/cluster_data/database
            type: DirectoryOrCreate
          name: database
        - hostPath:
            path:  /proc/sysrq-trigger
            type: File
          name: proc
        - name: mqtt-cert
          secret:
            secretName: mve-worker-secrets
        - emptyDir: {}
          name: tmp-dir
---
#Baetyl agent
apiVersion: v1
kind: ServiceAccount
metadata:
  name: baetyl-edge-system-service-account
  namespace: {{.EdgeSystemNamespace}}

---
# elevation of authority
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: baetyl-edge-system-rbac
subjects:
  - kind: ServiceAccount
    name: baetyl-edge-system-service-account
    namespace: {{.EdgeSystemNamespace}}
roleRef:
  kind: ClusterRole
  name: cluster-admin
  apiGroup: rbac.authorization.k8s.io

---
apiVersion: v1
kind: Secret
metadata:
  name: {{.NodeCertName}}
  namespace: {{.EdgeSystemNamespace}}
type: Opaque
data:
  client.pem: "{{.NodeCertPem}}"
  client.key: "{{.NodeCertKey}}"
  ca.pem: "{{.NodeCertCa}}"

---
# baetyl-init configmap
apiVersion: v1
kind: ConfigMap
metadata:
  name: baetyl-init-config
  namespace: {{.EdgeSystemNamespace}}
data:
  conf.yml: |-
    node:
      ca: var/lib/baetyl/node/ca.pem
      key: var/lib/baetyl/node/client.key
      cert: var/lib/baetyl/node/client.pem
    sync:
      download:
        timeout: 30m
    httplink:
      address: "{{GetProperty "sync-server-address"}}"
      insecureSkipVerify: true
    logger:
      level: debug
      encoding: console

---
# baetyl-init deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: baetyl-init
  namespace: {{.EdgeSystemNamespace}}
  labels:
    baetyl-app-name: "{{.InitAppName}}"
    baetyl-app-version: "{{.InitVersion}}"
    baetyl-service-name: baetyl-init
spec:
  selector:
    matchLabels:
      baetyl-service-name: baetyl-init
  replicas: 1
  template:
    metadata:
      labels:
        baetyl-app-name: baetyl-init
        baetyl-service-name: baetyl-init
    spec:
      # nodeName: {{.KubeNodeName}}
      tolerations:
        - key: node-role.kubernetes.io/master
          operator: Exists
          effect: NoSchedule
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: node-role.kubernetes.io/master
                    operator: Exists
      serviceAccountName: baetyl-edge-system-service-account
      containers:
        - name: baetyl-init
          image: {{GetModuleImage "baetyl"}}
          imagePullPolicy: IfNotPresent
          args:
            - init
          env:
            - name: BAETYL_APP_NAME
              value: "{{.InitAppName}}"
            - name: BAETYL_APP_VERSION
              value: "{{.InitVersion}}"
            - name: BAETYL_NODE_NAME
              value: "{{.NodeName}}"
            - name: BAETYL_SERVICE_NAME
              value: "baetyl-init"
            - name: BAETYL_RUN_MODE
              value: "kube"
            - name: KUBE_NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
          securityContext:
            privileged: true
          volumeMounts:
            - name: init-conf
              mountPath: /etc/baetyl
            - name: core-store-path
              mountPath: /var/lib/baetyl/store
            - name: object-download-path
              mountPath: /var/lib/baetyl/object
            - name: host-root-path
              mountPath: /var/lib/baetyl/host
            - name: node-cert
              mountPath: var/lib/baetyl/node
      volumes:
        - name: init-conf
          configMap:
            name: baetyl-init-config
        - name: core-store-path
          hostPath:
            path: /var/lib/baetyl/store
        - name: object-download-path
          hostPath:
            path: /var/lib/baetyl/object
        - name: host-root-path
          hostPath:
            path: /var/lib/baetyl/host
        - name: node-cert
          secret:
            secretName: {{.NodeCertName}}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: fluent-bit
  namespace: kube-system
  labels:
    app.kubernetes.io/name: fluent-bit
    app.kubernetes.io/instance: fluent-bit
    app.kubernetes.io/version: "2.1.4"
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: fluent-bit
  namespace: kube-system
  labels:
    app.kubernetes.io/name: fluent-bit
    app.kubernetes.io/instance: fluent-bit
    app.kubernetes.io/version: "2.1.4"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: fluent-bit
  namespace: kube-system
  labels:
    app.kubernetes.io/name: fluent-bit
    app.kubernetes.io/instance: fluent-bit
    app.kubernetes.io/version: "2.1.4"
data:
  labelmap.json: |
    {
      "kubernetes": {
        "container_name": "container",
        "labels": {
          "app.kubernetes.io/instance": "app",
          "app.kubernetes.io/version": "version"
        },
        "namespace_name": "namespace",
        "pod_name": "instance"
      },
      "level": "level"
    }
  fluent-bit-worker-ota.conf: |
    [INPUT]
        Name tail
        Path /var/log/worker-ota-syslog
        Tag worker-ota
        Mem_Buf_Limit 5MB
        Buffer_Chunk_size 32k
        Buffer_Max_size 32k
    [FILTER]
        Name grep
        Match worker-ota
        Regex log .*what.*
    [OUTPUT]
        Name loki
        Match worker-ota
        Host loki.mve2-spearow.knovel.org
        Port 443
        http_user loki
        http_passwd quR95hp_uTZa@el1
        TLS on
        TLS.Verify off
        Labels device={{.NodeName}}, app=worker-ota, version=1.0.0, level=INFO
  fluent-bit.conf: |
    [SERVICE]
        Daemon Off
        Flush 1
        Log_Level info
        Parsers_File parsers.conf
        HTTP_Server On
        HTTP_Listen 0.0.0.0
        HTTP_Port 2020
        Health_Check On

    [INPUT]
        Name tail
        Path /var/log/containers/*.log
        Tag kube.*
        multiline.parser go, docker, cri
        DB /run/fluent-bit/flb_kube.db
        Mem_Buf_Limit 5MB
        Mem_Buf_Limit     5MB
        Buffer_Chunk_size 32k
        Buffer_Max_size   32k

    [FILTER]
        Name kubernetes
        Match kube.*
        Kube_URL https://kubernetes.default.svc:443
        Merge_Log On
        Keep_Log Off
        K8S-Logging.Parser On
        K8S-Logging.Exclude Off

    [FILTER]
        Name           grep
        Match          kube.*
        Logical_Op     or
        Exclude        $kubernetes['namespace_name'] baetyl-edge-system
        Exclude        $kubernetes['namespace_name'] kube-system
        Exclude        $kubernetes['namespace_name'] default
        Exclude        $level DEBUG
        Exclude        $kubernetes['container_name'] stream
        Exclude        $kubernetes['container_name'] web

    [OUTPUT]
        name loki
        match kube.*
        host loki.mve2-spearow.knovel.org
        port 443
        http_user loki
        http_passwd quR95hp_uTZa@el1
        tls on
        tls.verify off
        labels device={{.NodeName}}
        drop_single_key true
        remove_keys kubernetes,stream
        auto_kubernetes_labels off
        label_map_path /fluent-bit/etc/labelmap.json
        line_format json

    @INCLUDE fluent-bit-worker-ota.conf
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: fluent-bit
  labels:
    app.kubernetes.io/name: fluent-bit
    app.kubernetes.io/instance: fluent-bit
    app.kubernetes.io/version: "2.1.4"
rules:
  - apiGroups:
      - ""
    resources:
      - namespaces
      - pods
    verbs:
      - get
      - list
      - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: fluent-bit
  labels:
    app.kubernetes.io/name: fluent-bit
    app.kubernetes.io/instance: fluent-bit
    app.kubernetes.io/version: "2.1.4"
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: fluent-bit
subjects:
  - kind: ServiceAccount
    name: fluent-bit
    namespace: kube-system
---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: fluent-bit
  namespace: kube-system
  labels:
    app.kubernetes.io/name: fluent-bit
    app.kubernetes.io/instance: fluent-bit
    app.kubernetes.io/version: "2.1.4"
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: fluent-bit
      app.kubernetes.io/instance: fluent-bit
  template:
    metadata:
      annotations:
        checksum/config: e9e9111ee63554f1f5428424f30c7083181fa0e7d625381e1835efe5e3ed3def
        checksum/luascripts: e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855
      labels:
        app.kubernetes.io/name: fluent-bit
        app.kubernetes.io/instance: fluent-bit
    spec:
      serviceAccountName: fluent-bit
      hostNetwork: false
      dnsPolicy: ClusterFirst
      containers:
        - name: fluent-bit
          image: "cr.fluentbit.io/fluent/fluent-bit:2.1.4"
          imagePullPolicy: IfNotPresent
          securityContext:
            privileged: true
          ports:
            - name: http
              containerPort: 2020
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /
              port: http
          readinessProbe:
            httpGet:
              path: /api/v1/health
              port: http
          volumeMounts:
            - mountPath: /fluent-bit/etc/fluent-bit-worker-ota.conf
              name: config
              subPath: fluent-bit-worker-ota.conf
            - mountPath: /fluent-bit/etc/fluent-bit.conf
              name: config
              subPath: fluent-bit.conf
            - mountPath: /fluent-bit/etc/labelmap.json
              name: config
              subPath: labelmap.json
            - mountPath: /var/log/worker-ota-syslog
              name: varlogsyslog
              readOnly: true
            - mountPath: /var/log/containers
              name: varlogcontainers
              readOnly: true
            - mountPath: /var/log/pods
              name: varlogpods
              readOnly: true
            - mountPath: /var/lib/docker/containers
              name: varlibdockercontainers
              readOnly: true
            - mountPath: /data/docker/containers
              name: datadockercontainers
              readOnly: true
            - name: run
              mountPath: /run/fluent-bit
      volumes:
        - name: config
          configMap:
            name: fluent-bit
        - hostPath:
            path: /var/log/syslog
            type: File
          name: varlogsyslog
        - hostPath:
            path: /var/log/containers
          name: varlogcontainers
        - hostPath:
            path: /var/log/pods
          name: varlogpods
        - hostPath:
            path: /var/lib/docker/containers
          name: varlibdockercontainers
        - hostPath:
            path: /data/docker/containers
          name: datadockercontainers
        - name: run
          hostPath:
            path: /run/fluent-bit
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: mve-redis
  namespace: {{.EdgeNamespace}}
spec:
  ports:
    - protocol: TCP
      port: 6379
      targetPort: 6379
      nodePort: 30079
  selector:
    app.kubernetes.io/component: redis
    app.kubernetes.io/instance: mve-redis
    app.kubernetes.io/name: mve-redis
    app.kubernetes.io/version: 1.0.0
  type: NodePort
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: mve-redis
  namespace: {{.EdgeNamespace}}
spec:
  replicas: 1
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app.kubernetes.io/component: redis
      app.kubernetes.io/instance: mve-redis
      app.kubernetes.io/name: mve-redis
      app.kubernetes.io/version: 1.0.0
  strategy:
    rollingUpdate:
      maxSurge: 100%
      maxUnavailable: 10%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app.kubernetes.io/component: redis
        app.kubernetes.io/instance: mve-redis
        app.kubernetes.io/name: mve-redis
        app.kubernetes.io/part-of: mve
        app.kubernetes.io/version: 1.0.0
    spec:
      volumes:
        - name: redis-persistence
          emptyDir:
            medium: "Memory"
      containers:
      - name: redis
        image: arm64v8/redis:7.0
        ports:
        - containerPort: 6379
        volumeMounts:
        - name: redis-persistence
          mountPath: /data
        resources:
          requests:
            memory: "64Mi"
            cpu: "250m"
          limits:
            memory: "128Mi"
            cpu: "500m"
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: mve-rtsp-server
  namespace: {{.EdgeNamespace}}
spec:
  ports:
    - name: http
      port: 8554
      nodePort: 30054
      protocol: TCP
      targetPort: http
  selector:
    app.kubernetes.io/component: rtsp-server
    app.kubernetes.io/instance: mve-rtsp-server
    app.kubernetes.io/name: mve-rtsp-server
    app.kubernetes.io/version: 1.0.0
  type: NodePort

---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/part-of: mve
    app.kubernetes.io/version: 1.0.0
  name: mve-rtsp-server
  namespace: {{.EdgeNamespace}}
spec:
  replicas: 1
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app.kubernetes.io/component: rtsp-server
      app.kubernetes.io/instance: mve-rtsp-server
      app.kubernetes.io/name: mve-rtsp-server
      app.kubernetes.io/version: 1.0.0
  strategy:
    rollingUpdate:
      maxSurge: 100%
      maxUnavailable: 10%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app.kubernetes.io/component: rtsp-server
        app.kubernetes.io/instance: mve-rtsp-server
        app.kubernetes.io/name: mve-rtsp-server
        app.kubernetes.io/part-of: mve
        app.kubernetes.io/version: 1.0.0
    spec:
      containers:
        - env:
            - name: K8S_POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: K8S_NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: MTX_PROTOCOLS
              value: tcp
          image: aler9/rtsp-simple-server:v1.10.0
          imagePullPolicy: IfNotPresent
          name: rtsp-server
          ports:
            - containerPort: 8554
              name: http
              protocol: TCP

          resources:
            limits:
              cpu: "4"
              memory: 8Gi
            requests:
              cpu: "1"
              memory: 2Gi
      imagePullSecrets: []
      securityContext: {}
      volumes:
        - emptyDir: {}
          name: tmp-dir