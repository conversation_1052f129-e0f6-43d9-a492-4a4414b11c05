name: "{{.CoreConfName}}"
namespace: "{{.Namespace}}"
system: true
labels:
  baetyl-app-name: "{{.CoreAppName}}"
  baetyl-node-name: "{{.NodeName}}"
  baetyl-cloud-system: "true"
data:
  conf.yml: |-
    node:
      ca: var/lib/baetyl/node/ca.pem
      key: var/lib/baetyl/node/client.key
      cert: var/lib/baetyl/node/client.pem
    server:
      key: var/lib/baetyl/system/certs/key.pem
      cert: var/lib/baetyl/system/certs/crt.pem
    sync:
      download:
        timeout: 30m
      report:
        interval: {{.CoreFrequency}}
    httplink:
      address: "{{GetProperty "sync-server-address"}}"
      insecureSkipVerify: true
    logger:
      level: debug
      encoding: console