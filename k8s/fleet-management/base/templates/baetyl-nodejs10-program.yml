name: "{{.ConfigName}}"
namespace: "{{.Namespace}}"
system: true
labels:
  baetyl-cloud-system: "true"
  baetyl-config-type: "baetyl-program"
data:
  _object_baetyl-nodejs10_darwin-amd64.zip: |-
    {"url":"{{GetModuleProgram "nodejs10" "darwin-amd64"}}","unpack":"zip"}
  _object_baetyl-nodejs10_linux-amd64.zip: |-
    {"url":"{{GetModuleProgram "nodejs10" "linux-amd64"}}","unpack":"zip"}
  _object_baetyl-nodejs10_linux-arm64-v8.zip: |-
    {"url":"{{GetModuleProgram "nodejs10" "linux-arm64-v8"}}","unpack":"zip"}
  _object_baetyl-nodejs10_linux-arm-v7.zip: |-
    {"url":"{{GetModuleProgram "nodejs10" "linux-arm-v7"}}","unpack":"zip"}