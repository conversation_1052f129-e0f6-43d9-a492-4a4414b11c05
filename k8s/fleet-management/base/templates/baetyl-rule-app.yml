name: "{{.RuleAppName}}"
namespace: "{{.Namespace}}"
selector: "baetyl-node-name={{.NodeName}}"
labels:
  baetyl-cloud-system: "true"
  baetyl-app-mode: "{{.AppMode}}"
type: "container"
mode: "{{.NodeMode}}"
system: true
replica: 1
services:
  - name: "baetyl-rule"
    type: "deployment"
    image: {{GetModuleImage "baetyl-rule"}}
    replica: 1
    volumeMounts:
      - name: "rule-conf"
        mountPath: "/etc/baetyl"
        readOnly: true
volumes:
  - name: "rule-conf"
    config:
      name: "{{.RuleConfName}}"
      version: "{{.RuleConfVersion}}"
