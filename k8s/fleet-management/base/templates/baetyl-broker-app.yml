name: "{{.BrokerAppName}}"
namespace: "{{.Namespace}}"
selector: "baetyl-node-name={{.NodeName}}"
labels:
  baetyl-cloud-system: "true"
  baetyl-app-mode: "{{.AppMode}}"
type: "container"
mode: "{{.NodeMode}}"
system: true
replica: 1
services:
  - name: "baetyl-broker"
    type: "deployment"
    image: {{GetModuleImage "baetyl-broker"}}
    replica: 1
    volumeMounts:
      - name: "broker-conf"
        mountPath: "/etc/baetyl"
        readOnly: true
    ports:
      - containerPort: 50010
        protocol: "TCP"
volumes:
  - name: "broker-conf"
    config:
      name: "{{.BrokerConfName}}"
      version: "{{.BrokerConfVersion}}"
