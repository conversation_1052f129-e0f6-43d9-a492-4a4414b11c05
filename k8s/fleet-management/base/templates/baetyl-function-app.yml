name: "{{.FunctionAppName}}"
namespace: "{{.Namespace}}"
selector: "baetyl-node-name={{.NodeName}}"
labels:
  baetyl-cloud-system: "true"
  baetyl-app-mode: "{{.AppMode}}"
  resource-invisible: "true"
type: "container"
mode: "{{.NodeMode}}"
system: true
replica: 1
services:
  - name: "baetyl-function"
    image: "{{GetModuleImage "baetyl-function"}}"
    type: "deployment"
    replica: 1
    volumeMounts:
      - name: "func-conf"
        mountPath: "/etc/baetyl"
        readOnly: true
    ports:
      - containerPort: 50011
        protocol: "TCP"
volumes:
  - name: "func-conf"
    config:
      name: "{{.FunctionConfName}}"
      version: "{{.FunctionConfVersion}}"
