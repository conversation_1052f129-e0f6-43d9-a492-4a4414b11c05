apiVersion: apps/v1
kind: Deployment
metadata:
  name: fleet-management
  annotations:
    configmap.reloader.stakater.com/reload: "fleet-management-template-config"
spec:
  replicas: 1
  revisionHistoryLimit: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: "10%"
      maxSurge: "100%"
  template:
    spec:
      serviceAccount: fleet-management
      imagePullSecrets: []
      containers:
        - name: fleet-management
          image: "fleet-management"
          imagePullPolicy: Always
          command:
            - baetyl-cloud
          args:
            - -c
            - /etc/baetyl/conf.yml
          ports:
            - containerPort: 9003
              name: init-port
              protocol: TCP
            - containerPort: 9004
              name: admin-port
              protocol: TCP
            - containerPort: 9005
              name: sync-port
              protocol: TCP
          volumeMounts:
            - mountPath: /etc/baetyl
              name: configs
            - mountPath: /etc/templates
              name: templates
            - mountPath: /etc/certs
              name: certs
          livenessProbe:
            httpGet:
              path: /health
              port: admin-port
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          readinessProbe:
            httpGet:
              path: /health
              port: admin-port
            failureThreshold: 3
            initialDelaySeconds: 15
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: "500m"
              memory: 1Gi
            requests:
              cpu: "100m"
              memory: "1024Mi"
      volumes:
        - name: templates
          configMap:
            name: fleet-management-template-config
        - name: certs
          secret:
            secretName: fleet-management-certs-secret
        - name: configs
          secret:
            secretName: fleet-management-configs-secret
