apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

commonLabels:
  app.kubernetes.io/name: fleet-management
  app.kubernetes.io/instance: fleet-management
  app.kubernetes.io/component: fleet-management

labels:
  - pairs:
      app.kubernetes.io/part-of: mve
    includeSelectors: false
    includeTemplates: true

generatorOptions:
  disableNameSuffixHash: true

configMapGenerator:
  - name: fleet-management-template-config
    files:
      - templates/baetyl-broker-app.yml
      - templates/baetyl-broker-conf.yml
      - templates/baetyl-core-app.yml
      - templates/baetyl-core-conf.yml
      - templates/baetyl-ekuiper-app.yml
      - templates/baetyl-function-app.yml
      - templates/baetyl-function-conf.yml
      - templates/baetyl-init-app.yml
      - templates/baetyl-init-conf.yml
      - templates/baetyl-install.sh
      - templates/baetyl-nodejs10-program.yml
      - templates/baetyl-python3-opencv-program.yml
      - templates/baetyl-python3-program.yml
      - templates/baetyl-rule-app.yml
      - templates/baetyl-rule-conf.yml
      - templates/baetyl-sql-program.yml

resources:
  - namespace.yml
  - crds.yml
  - rbac.yml
  - service-account.yml
  - deployment.yml
  - service.yml
