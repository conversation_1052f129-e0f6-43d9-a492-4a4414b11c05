kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: fleet-management-cluster-role
rules:
  - apiGroups: [ "cloud.baetyl.io" ]
    resources: [ "secrets","applications","nodes","configurations","nodedesires","nodereports" ]
    verbs: [ "get", "list", "watch", "create", "update", "patch", "delete" ]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: fleet-management-role-binding
subjects:
  - kind: ServiceAccount
    name: fleet-management
    namespace: baetyl-cloud
roleRef:
  kind: ClusterRole
  name: fleet-management-cluster-role
  apiGroup: rbac.authorization.k8s.io