apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
namespace: mve
commonLabels:
  app.kubernetes.io/environment: spearow
generatorOptions:
  disableNameSuffixHash: true
configMapGenerator:
  - name: cloud-ui-config
    literals:
      - DEBUG="false"
      - VITE_API_ENDPOINT="https://api.mve2-spearow.knovel.org/api/v1"
      - VITE_APP_WEBSOCKET_URL="https://api.mve2-spearow.knovel.org/socket.io"
      - VITE_APP_FOOTER="Powered by Knizsoft"
      - TZ="Asia/Singapore"
resources:
  - ../../base
  - route-ingress.yml
images:
  - name: "cloud-ui"
    newName: "572881485767.dkr.ecr.ap-southeast-1.amazonaws.com/cloud-ui"
    newTag: "v2.0.0-alpha.3"
