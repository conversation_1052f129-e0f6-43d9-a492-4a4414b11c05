apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
namespace: mve
commonLabels:
  app.kubernetes.io/environment: staging #CHANGE_FOR_NEW_ENV
generatorOptions:
  disableNameSuffixHash: true
configMapGenerator:
  - name: cloud-ui-config
    literals:
      - DEBUG="false"
      - VITE_API_ENDPOINT="https://api.mve2-staging.knizsoft.com/api/v1" #CHANGE_FOR_NEW_ENV #CHANGE_IF_NOT_USING_KNOVEL
      - VITE_APP_WEBSOCKET_URL="https://api.mve2-staging.knizsoft.com/socket.io" #CHANGE_FOR_NEW_ENV #CHANGE_IF_NOT_USING_KNOVEL
      - VITE_APP_FOOTER="Powered by Knizsoft"
resources:
  - ../../base
  - route-ingress.yml
images:
  - name: "cloud-ui"
    newName: "572881485767.dkr.ecr.ap-southeast-1.amazonaws.com/cloud-ui" #CHANGE_IF_NECESSARY
    newTag: "dfa56cfastg"
