apiVersion: apps/v1
kind: Deployment
metadata:
  name: cloud-ui
spec:
  selector: 
    matchLabels:
      app: cloud-ui
  replicas: 1
  revisionHistoryLimit: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: "10%"
      maxSurge: "100%"
  template:
    metadata:
      labels:
        app: cloud-ui
    spec:
      serviceAccount: cloud-ui
      imagePullSecrets: []
      containers:
        - name: cloud-ui
          image: "cloud-ui"
          imagePullPolicy: Always
          envFrom:
            - configMapRef:
                name: cloud-ui-config
          ports:
            - name: http
              containerPort: 80
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /
              port: http
            initialDelaySeconds: 3
            failureThreshold: 3
            successThreshold: 1
            periodSeconds: 5
            timeoutSeconds: 5
          readinessProbe:
            httpGet:
              path: /
              port: http
            initialDelaySeconds: 3
            failureThreshold: 3
            successThreshold: 1
            periodSeconds: 5
            timeoutSeconds: 5
          resources:
            limits:
              cpu: "500m"
              memory: 1Gi
            requests:
              cpu: "10m"
              memory: "50Mi"
