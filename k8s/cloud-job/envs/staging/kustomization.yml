apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
namespace: baetyl-cloud

commonLabels:
  app.kubernetes.io/environment: staging #CHANGE_FOR_DIFF_ENV

generatorOptions:
  disableNameSuffixHash: true

configMapGenerator:
  - name: ecr-edge-helper-configmap
    literals:
      - AWS_REGION=ap-southeast-1
      - AWS_ACCOUNT=************ #CHANGE_IF_NECESSARY

resources:
  - service-account.yml
  - ../../base
