apiVersion: batch/v1
kind: CronJob
metadata:
  name: ecr-edge-helper-update
spec:
  schedule: "0 */10 * * *"
  successfulJobsHistoryLimit: 3
  suspend: false
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: ecr-edge-helper
          containers:
            - name: aws-cli
              image: ducmeit1/aws-kubectl:3.18-amd
              imagePullPolicy: IfNotPresent
              envFrom:
                - configMapRef:
                    name: ecr-edge-helper-configmap
              volumeMounts:
                - name: script-volume
                  mountPath: /tmp/script.sh
                  subPath: update-script.sh
              command:
                - /bin/sh
                - -c
                - /tmp/script.sh
          volumes:
            - name: script-volume
              configMap:
                name: ecr-edge-helper-script-configmap
                defaultMode: 0755
          restartPolicy: OnFailure
