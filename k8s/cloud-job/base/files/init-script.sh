#!/bin/sh
aws sts get-caller-identity
ECR_TOKEN=`aws ecr get-login-password --region ${AWS_REGION}`
JSON=$(jq -n -c --arg name "privateecr" --arg address "https://${AWS_ACCOUNT}.dkr.ecr.${AWS_REGION}.amazonaws.com" --arg username "AWS" --arg password "${ECR_TOKEN}" '$ARGS.named')
echo "Waiting fleet management server is ready" ...
sleep 30
echo "Delete previous secret"
curl -X DELETE http://fleet-management:9004/v1/registries/privateecr -vvv
echo "Create new secret"
curl -X POST http://fleet-management:9004/v1/registries -H "Content-Type: application/json" -d "$JSON" -vvv