apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
namespace: baetyl-cloud

commonLabels:
  app.kubernetes.io/name: cloud-job
  app.kubernetes.io/instance: cloud-job
  app.kubernetes.io/component: cloud-job

generatorOptions:
  disableNameSuffixHash: true

configMapGenerator:
  - name: ecr-edge-helper-script-configmap
    files:
      - files/init-script.sh
      - files/update-script.sh

resources:
  - ecr-edge-helper-job.yml
  - ecr-edge-helper-cronjob.yml
