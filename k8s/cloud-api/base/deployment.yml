apiVersion: apps/v1
kind: Deployment
metadata:
  name: cloud-api
spec:
  replicas: 1
  revisionHistoryLimit: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: "10%"
      maxSurge: "100%"
  template:
    spec:
      serviceAccount: cloud-api
      imagePullSecrets: []
      containers:
        - name: cloud-api
          image: "cloud-api"
          imagePullPolicy: Always
          command:
            - /app/server
          args:
            - api
          envFrom:
            - configMapRef:
                name: cloud-api-config
            - secretRef:
                name: cloud-api-secret
          ports:
            - name: http
              containerPort: 8000
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /ready/liveliness
              port: http
            initialDelaySeconds: 3
            failureThreshold: 3
            successThreshold: 1
            periodSeconds: 5
            timeoutSeconds: 5
          readinessProbe:
            httpGet:
              path: /ready/readiness
              port: http
            initialDelaySeconds: 3
            failureThreshold: 3
            successThreshold: 1
            periodSeconds: 5
            timeoutSeconds: 5
          resources:
            limits:
              cpu: "500m"
              memory: 1Gi
            requests:
              cpu: "25m"
              memory: "50Mi"
