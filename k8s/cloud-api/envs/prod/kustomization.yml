apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
namespace: mve
commonLabels:
  app.kubernetes.io/environment: prod
generatorOptions:
  disableNameSuffixHash: true
configMapGenerator:
  - name: cloud-api-config
    literals:
      - DEBUG="false"
      - BAETYL_HOST="http://fleet-management.baetyl-cloud.svc.cluster.local:9004"
      - BAETYL_FETCH_TIME="3"
      - SOCKET_FETCH_TIME="1"
      - MQTT_HOST="mqtt-internal.kube-system.svc.cluster.local"
      - MQTT_PORT="1883"
      - S3_BUCKET="mve2-prod-asset.knovel.org"
      - S3_REGION="ap-southeast-1"
      - MQTT_STATS_TOPIC="stats"
      - MQTT_DETECTION_TOPIC="detection"
      - DEVICE_CPU_THRESHOLD_PER="80"
      - DEVICE_MEM_THRESHOLD_PER="80"
      - DEVICE_STORAGE_THRESHOLD_PER="80"
      - DEVICE_TEMP_THRESHOLD="80"
      - TZ="Asia/Singapore"
resources:
  - service-account.yml
  - ../../base
  - route-ingress.yml
images:
  - name: "cloud-api"
    newName: "************.dkr.ecr.ap-southeast-1.amazonaws.com/cloud-api"
    newTag: "v1.1.6"
