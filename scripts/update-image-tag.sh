#!/bin/bash
APPLICATION=${APPLICATION:-empty}
REPOSITORY_NAME=${REPOSITORY_NAME:-empty}
TAG_NAME=${TAG_NAME:-empty}
KUSTOMIZE_ENV=${KUSTOMIZE_ENV:-empty}
COMMIT=${CI_COMMIT_SHORT_SHA:-empty}

echo "APPLICATION: $APPLICATION"
echo "REPOSITORY_NAME: $REPOSITORY_NAME"
echo "TAG_NAME: $TAG_NAME"
echo "KUSTOMIZE_ENV: $KUSTOMIZE_ENV"
echo "COMMIT: $COMMIT"

KUSTOMIZATION_FILE="k8s/$APPLICATION/envs/$KUSTOMIZE_ENV/kustomization.yml"

function try()
{
    [[ $- = *e* ]]; SAVED_OPT_E=$?
    set +e
}

function throw()
{
    exit $1
}

function catch()
{
    export ex_code=$?
    (( $SAVED_OPT_E )) && set +e
    return $ex_code
}

function throwErrors()
{
    set -e
}

function ignoreErrors()
{
    set +e
}

export CloneCodeFailure=100
export PushCodeFailure=101
export KustomizationFileNotFound=102

# start with a try
try
(   # open a subshell !!!
    git config --global core.autocrlf false
    git config --global http.sslVerify false
    git config --global user.email "<EMAIL>"
    git config --global user.name "Gitlab CI Bot"

    [ ! -f "$KUSTOMIZATION_FILE" ] && throw $KustomizationFileNotFound

    echo "Replacing..."
    URL=$REPOSITORY_NAME TAG=$TAG_NAME yq e -i '.images[0].newName = strenv(URL) | .images[0].newTag = strenv(TAG)' $KUSTOMIZATION_FILE
    yq e -C $KUSTOMIZATION_FILE

    echo "Commiting changes..."
    git add . 
    git commit -m "Update $APPLICATION to commit $COMMIT on $KUSTOMIZE_ENV environment" || echo "our $KUSTOMIZE_ENV is up to date"

    echo "Pushing changes..."
    git -c http.sslVerify=false push
    [ $? != 0 ] && throw $PushCodeFailure

    echo "Patching done!"
)
# directly after closing the subshell you need to connect a group to the catch using ||
catch || {
    # now you can handle
    case $ex_code in
        $CloneCodeFailure)
            echo "Unable to clone repository!"
        ;;
        $PushCodeFailure)
            echo "Cannot push changes to repository!"
        ;;
        $KustomizationFileNotFound)
            echo "Kustomization file path is not found!"
        ;;        
        *)
            echo "An unexpected exception was thrown"
            throw $ex_code # you can rethrow the "exception" causing the script to exit if not caught
        ;;
    esac
}