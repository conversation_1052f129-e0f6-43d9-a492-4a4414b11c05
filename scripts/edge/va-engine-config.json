{"name": "va-engine-2", "selector": "node=orin-node-22", "description": "VA Engine app", "labels": {"app.kubernetes.io/name": "mve-vaengine", "app.kubernetes.io/instance": "mve-vaengine", "app.kubernetes.io/component": "va-engine", "app.kubernetes.io/part-of": "mve", "app.kubernetes.io/version": "1.0.0"}, "services": [{"name": "va-engine", "image": "phuclb1/va-engine:k3s", "replica": 1, "volumeMounts": [{"name": "data", "mountPath": "/mnt/data"}, {"name": "database", "mountPath": "/mnt/database"}], "ports": []}], "volumes": [{"name": "data", "hostPath": {"path": "/data/cluster_data/data"}}, {"name": "database", "hostPath": {"path": "/data/cluster_data/database"}}], "jobConfig": {"restartPolicy": "Always"}, "registries": [{"name": "privateecr"}]}