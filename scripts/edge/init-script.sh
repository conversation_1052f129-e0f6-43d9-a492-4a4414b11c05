#!/bin/sh
SUDO=sudo
DOKCERDAEMON='{ \
    \"runtimes\": {\
        \"nvidia\": {\
            \"path\": \"nvidia-container-runtime\",\
            \"runtimeArgs\": []\
        }\
    },\
    \"default-runtime\": \"nvidia\",\
    \"data-root\": \"/data/docker\"\
}'
exec_cmd_nobail() {
  echo "+ $2 bash -c \"$1\""
  $2 bash -c "$1"
}

print_status() {
  echo "## $1"
}
mount_data_folder() {
  exec_cmd_nobail "mkfs.ext4 /dev/nvme0n1" $SUDO
  exec_cmd_nobail 'echo "/dev/nvme0n1 /data ext4 defaults 0 1" >> /etc/fstab' $SUDO
}

setup_docker() {
    exec_cmd_nobail "apt-get update" $SUDO
    exec_cmd_nobail "apt-get install -y apt-transport-https ca-certificates curl gnupg lsb-release" $SUDO
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
    echo \
    "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu \
    $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
    exec_cmd_nobail "apt-get update" $SUDO
    exec_cmd_nobail "apt-get install -y docker-ce docker-ce-cli containerd.io" $SUDO
    exec_cmd_nobail "groupadd docker" $SUDO
    exec_cmd_nobail "usermod -aG docker $USER" $SUDO
    exec_cmd_nobail "mkdir -p /data/docker" $SUDO # create folder
    exec_cmd_nobail "echo $DOKCERDAEMON> /etc/docker/daemon.json" $SUDO # create daemon.json
    exec_cmd_nobail "systemctl restart docker" $SUDO # restart docker
}

echo "Mounting data folder..."
mount_data_folder
echo "Setting up docker..."
setup_docker